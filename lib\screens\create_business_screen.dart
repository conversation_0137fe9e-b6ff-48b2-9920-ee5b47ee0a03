import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class CreateBusinessScreen extends StatefulWidget {
  const CreateBusinessScreen({super.key});

  @override
  _CreateBusinessScreenState createState() => _CreateBusinessScreenState();
}

class _CreateBusinessScreenState extends State<CreateBusinessScreen> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final EcommerceService _ecommerceService = EcommerceService();
  bool _isLoading = false;

  final ImagePicker _picker = ImagePicker();
  final List<XFile> _businessImages = [];

  Future<void> _pickImages() async {
    final List<XFile> pickedFiles = await _picker.pickMultiImage();
    setState(() {
      _businessImages.addAll(pickedFiles);
    });
  }

  void _removeImage(int index) {
    setState(() {
      _businessImages.removeAt(index);
    });
  }

  void _submit() async {
    if (_nameController.text.trim().isEmpty || _businessImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Please provide a business name and at least one image.',
          ),
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      // --- THE FIX: Corrected the call to the service ---
      await _ecommerceService.createBusiness(
        businessName: _nameController.text,
        description: _descriptionController.text,
        images: _businessImages,
      );
      // --- End of FIX ---

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Business created successfully!')),
        );
        // TODO: Navigate to the new inventory screen
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0), // Light beige background
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'Create Your Business',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Business Name',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            NeuCard(
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                controller: _nameController,
                decoration: const InputDecoration(border: InputBorder.none),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Description (Optional)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            NeuCard(
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: TextField(
                controller: _descriptionController,
                maxLines: 4,
                decoration: const InputDecoration(border: InputBorder.none),
              ),
            ),
            const SizedBox(height: 24),
            // --- NEW: Image Picker and Preview Section ---
            const Text(
              'Business Images (First is Cover)',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: _businessImages.isEmpty
                  ? GestureDetector(
                      onTap: _pickImages,
                      child: NeuCard(
                        margin: EdgeInsets.zero,
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                EvaIcons.imageOutline,
                                size: 48,
                                color: Colors.grey,
                              ),
                              SizedBox(height: 8),
                              Text('Add at least one image'),
                            ],
                          ),
                        ),
                      ),
                    )
                  : GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                          ),
                      itemCount:
                          _businessImages.length + 1, // +1 for the add button
                      itemBuilder: (context, index) {
                        if (index == _businessImages.length) {
                          return GestureDetector(
                            onTap: _pickImages,
                            child: const NeuCard(
                              margin: EdgeInsets.zero,
                              child: Center(
                                child: Icon(EvaIcons.plus, size: 32),
                              ),
                            ),
                          );
                        }
                        return NeuCard(
                          margin: EdgeInsets.zero,
                          padding: EdgeInsets.zero,
                          child: Stack(
                            fit: StackFit.expand,
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(13),
                                child: Image.file(
                                  File(_businessImages[index].path),
                                  fit: BoxFit.cover,
                                ),
                              ),
                              Positioned(
                                top: 4,
                                right: 4,
                                child: GestureDetector(
                                  onTap: () => _removeImage(index),
                                  child: const NeuCard(
                                    margin: EdgeInsets.zero,
                                    padding: EdgeInsets.all(2),
                                    backgroundColor: Color(0xFFFF6B6B),
                                    child: Icon(
                                      Icons.close,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
            ),
            // --- End of NEW Section ---
            const SizedBox(height: 16),
            GestureDetector(
              onTap: _isLoading ? null : _submit,
              child: NeuCard(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(16),
                backgroundColor: const Color(0xFF00D2D3), // Cyan
                child: Center(
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 3,
                            color: Colors.white,
                          ),
                        )
                      : const Text(
                          'Create Business',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
