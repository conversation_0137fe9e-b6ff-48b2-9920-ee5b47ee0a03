import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:async';
import 'dart:io';

class NetworkTestScreen extends StatefulWidget {
  const NetworkTestScreen({super.key});

  @override
  State<NetworkTestScreen> createState() => _NetworkTestScreenState();
}

class _NetworkTestScreenState extends State<NetworkTestScreen> {
  final List<String> _testResults = [];
  bool _isRunning = false;
  
  final String _currentIp = defaultTargetPlatform == TargetPlatform.android
      ? "*************"
      : "127.0.0.1";

  void _addResult(String message) {
    setState(() {
      _testResults.add('${DateTime.now().toLocal().toString().substring(11, 19)}: $message');
    });
  }

  Future<void> _runNetworkTests() async {
    setState(() {
      _isRunning = true;
      _testResults.clear();
    });

    _addResult('🔍 Starting network diagnostics...');
    _addResult('📱 Platform: ${defaultTargetPlatform.name}');
    _addResult('🌐 Target IP: $_currentIp');

    // Test 1: Basic connectivity
    await _testBasicConnectivity();
    
    // Test 2: HTTP connection with timeout
    await _testHttpConnection();
    
    // Test 3: Specific API endpoints
    await _testApiEndpoints();

    setState(() {
      _isRunning = false;
    });
    
    _addResult('✅ Network diagnostics completed');
  }

  Future<void> _testBasicConnectivity() async {
    _addResult('');
    _addResult('🔌 Testing basic connectivity...');
    
    try {
      // Test socket connection
      final socket = await Socket.connect(_currentIp, 5000)
          .timeout(const Duration(seconds: 5));
      await socket.close();
      _addResult('✅ Socket connection successful');
    } catch (e) {
      _addResult('❌ Socket connection failed: $e');
    }
  }

  Future<void> _testHttpConnection() async {
    _addResult('');
    _addResult('🌐 Testing HTTP connection...');
    
    try {
      final client = http.Client();
      final response = await client
          .get(Uri.parse('http://$_currentIp:5000'))
          .timeout(const Duration(seconds: 10));
      
      _addResult('✅ HTTP connection successful');
      _addResult('📊 Status: ${response.statusCode}');
      _addResult('📏 Response length: ${response.body.length} chars');
      
      client.close();
    } catch (e) {
      _addResult('❌ HTTP connection failed: $e');
    }
  }

  Future<void> _testApiEndpoints() async {
    _addResult('');
    _addResult('🔗 Testing API endpoints...');
    
    final endpoints = [
      '/api/health',
      '/api/auth/login',
      '/api/auth/register',
    ];

    for (final endpoint in endpoints) {
      try {
        final client = http.Client();
        final response = await client
            .get(Uri.parse('http://$_currentIp:5000$endpoint'))
            .timeout(const Duration(seconds: 5));
        
        _addResult('✅ $endpoint: ${response.statusCode}');
        client.close();
      } catch (e) {
        _addResult('❌ $endpoint: $e');
      }
    }
  }

  Future<void> _testLoginRequest() async {
    _addResult('');
    _addResult('🔐 Testing login request...');
    
    try {
      final client = http.Client();
      final response = await client.post(
        Uri.parse('http://$_currentIp:5000/api/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: '{"email": "<EMAIL>", "password": "testpass"}',
      ).timeout(const Duration(seconds: 10));
      
      _addResult('✅ Login request completed');
      _addResult('📊 Status: ${response.statusCode}');
      _addResult('📄 Response: ${response.body.substring(0, response.body.length > 100 ? 100 : response.body.length)}...');
      
      client.close();
    } catch (e) {
      _addResult('❌ Login request failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Network Diagnostics'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Current Configuration',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Platform: ${defaultTargetPlatform.name}'),
                    Text('Target Server: http://$_currentIp:5000'),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isRunning ? null : _runNetworkTests,
                            icon: _isRunning 
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Icon(Icons.network_check),
                            label: Text(_isRunning ? 'Testing...' : 'Run Diagnostics'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: _isRunning ? null : _testLoginRequest,
                          icon: const Icon(Icons.login),
                          label: const Text('Test Login'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Test Results:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _testResults.isEmpty
                    ? const Center(
                        child: Text(
                          'No tests run yet.\nTap "Run Diagnostics" to start.',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: _testResults.length,
                        itemBuilder: (context, index) {
                          final result = _testResults[index];
                          Color textColor = Colors.black;
                          
                          if (result.contains('✅')) {
                            textColor = Colors.green;
                          } else if (result.contains('❌')) {
                            textColor = Colors.red;
                          } else if (result.contains('🔍') || result.contains('🔌') || result.contains('🌐') || result.contains('🔗') || result.contains('🔐')) {
                            textColor = Colors.blue;
                          }
                          
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Text(
                              result,
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                                color: textColor,
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              color: Colors.yellow.shade50,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '💡 Troubleshooting Tips:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      '• Ensure your server is running on the target IP\n'
                      '• Check that both devices are on the same network\n'
                      '• Verify firewall settings on the server\n'
                      '• Try pinging the IP from your computer first',
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
