import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';

class CustomMapMarker extends StatelessWidget {
  final String? placeName;
  final String? category;
  final double? rating;

  const CustomMapMarker({
    super.key,
    this.placeName,
    this.category,
    this.rating,
  });

  // A helper method to get the right icon for each category
  IconData _getIconForCategory(String? category) {
    switch (category) {
      case 'Restaurant':
        return PhosphorIcons.bowlSteam();
      case 'Cafe':
        return PhosphorIcons.coffee();
      case 'Shop':
        return PhosphorIcons.shoppingCart();
      case 'Art Gallery':
        return PhosphorIcons.palette();
      default:
        return PhosphorIcons.mapPinSimple(); // Default pin icon
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (placeName != null && placeName!.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              placeName!,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        if (placeName != null && placeName!.isNotEmpty)
          const SizedBox(height: 4),

        // The icon now dynamically changes based on the category
        Icon(
          _getIconForCategory(category),
          size: 30,
          color: const Color.fromARGB(255, 179, 53, 204),
        ),

        if (rating != null && rating! > 0)
          RatingBar.builder(
            initialRating: rating!,
            minRating: 1,
            direction: Axis.horizontal,
            allowHalfRating: true,
            itemCount: 5,
            itemSize: 18.0,
            itemPadding: const EdgeInsets.symmetric(horizontal: 1.0),
            itemBuilder: (context, _) =>
                const Icon(Icons.star, color: Colors.amber),
            onRatingUpdate: (rating) {},
            ignoreGestures: true,
          ),
      ],
    );
  }
}
