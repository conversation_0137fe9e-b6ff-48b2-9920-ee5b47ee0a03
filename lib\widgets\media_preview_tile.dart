// import 'dart:io';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:video_player/video_player.dart';
// import 'package:video_thumbnail/video_thumbnail.dart';
// import 'package:mime/mime.dart';

// class MediaPreviewTile extends StatefulWidget {
//   final XFile mediaFile;
//   final VoidCallback onRemove;

//   const MediaPreviewTile({
//     super.key,
//     required this.mediaFile,
//     required this.onRemove,
//   });

//   @override
//   State<MediaPreviewTile> createState() => _MediaPreviewTileState();
// }

// class _MediaPreviewTileState extends State<MediaPreviewTile> {
//   VideoPlayerController? _videoController;
//   bool _isVideo = false;
//   Uint8List? _fileBytes;
//   Uint8List? _videoThumbnail;
//   bool _isLoading = true;
//   bool _hasError = false;

//   @override
//   void initState() {
//     super.initState();
//     _initialize();
//   }

//   Future<void> _initialize() async {
//     _isVideo = _isVideoFile(widget.mediaFile);

//     try {
//       if (_isVideo) {
//         await _initializeVideo();
//       } else {
//         await _initializeImage();
//       }
//     } catch (e) {
//       debugPrint("Error initializing media preview: $e");
//       if (mounted) {
//         setState(() {
//           _hasError = true;
//         });
//       }
//     } finally {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }

//   Future<void> _initializeVideo() async {
//     try {
//       // Generate video thumbnail for preview
//       if (!kIsWeb) {
//         _videoThumbnail = await VideoThumbnail.thumbnailData(
//           video: widget.mediaFile.path,
//           imageFormat: ImageFormat.JPEG,
//           maxWidth: 200,
//           quality: 75,
//         );
//       } else {
//         // For web, we'll use the video player's first frame
//         _videoController = VideoPlayerController.networkUrl(Uri.parse(widget.mediaFile.path));
//         await _videoController!.initialize();
//       }
//     } catch (e) {
//       debugPrint("Error generating video thumbnail: $e");
//       // Fallback: try to initialize video controller anyway
//       try {
//         if (kIsWeb) {
//           _videoController = VideoPlayerController.networkUrl(Uri.parse(widget.mediaFile.path));
//         } else {
//           _videoController = VideoPlayerController.file(File(widget.mediaFile.path));
//         }
//         await _videoController!.initialize();
//       } catch (e2) {
//         debugPrint("Error initializing video controller: $e2");
//         rethrow;
//       }
//     }
//   }

//   Future<void> _initializeImage() async {
//     _fileBytes = await widget.mediaFile.readAsBytes();
//   }

//   bool _isVideoFile(XFile file) {
//     // First check the provided mimeType
//     String? mimeType = file.mimeType;
//     if (mimeType != null && mimeType.startsWith('video/')) {
//       return true;
//     }

//     // Fallback: use mime package to detect from file extension
//     String? detectedMimeType = lookupMimeType(file.path);
//     if (detectedMimeType != null && detectedMimeType.startsWith('video/')) {
//       return true;
//     }

//     // Final fallback: check file extension
//     String path = file.path.toLowerCase();
//     return path.endsWith('.mp4') ||
//            path.endsWith('.mov') ||
//            path.endsWith('.avi') ||
//            path.endsWith('.mkv') ||
//            path.endsWith('.webm') ||
//            path.endsWith('.3gp');
//   }

//   @override
//   void dispose() {
//     _videoController?.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(right: 8.0),
//       child: Stack(
//         clipBehavior: Clip.none,
//         children: [
//           ClipRRect(
//             borderRadius: BorderRadius.circular(8.0),
//             child: _buildPreviewWidget(),
//           ),
//           Positioned(
//             top: -8, right: -8,
//             child: GestureDetector(
//               onTap: widget.onRemove,
//               child: Container(
//                 decoration: const BoxDecoration(color: Colors.black54, shape: BoxShape.circle),
//                 child: const Icon(Icons.close, color: Colors.white, size: 18),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildPreviewWidget() {
//     const double size = 100;

//     if (_isLoading) {
//       return Container(
//         width: size,
//         height: size,
//         color: Colors.grey.shade200,
//         child: const Center(child: CircularProgressIndicator()),
//       );
//     }

//     if (_hasError) {
//       return Container(
//         width: size,
//         height: size,
//         color: Colors.grey.shade300,
//         child: const Center(
//           child: Icon(Icons.error, color: Colors.red, size: 30),
//         ),
//       );
//     }

//     if (_isVideo) {
//       return _buildVideoPreview(size);
//     } else {
//       return _buildImagePreview(size);
//     }
//   }

//   Widget _buildVideoPreview(double size) {
//     return Stack(
//       children: [
//         // Video thumbnail or first frame
//         Container(
//           width: size,
//           height: size,
//           color: Colors.black,
//           child: _videoThumbnail != null
//               ? Image.memory(
//                   _videoThumbnail!,
//                   width: size,
//                   height: size,
//                   fit: BoxFit.cover,
//                 )
//               : (_videoController?.value.isInitialized ?? false)
//                   ? VideoPlayer(_videoController!)
//                   : const Center(
//                       child: Icon(Icons.videocam_off, color: Colors.white, size: 30),
//                     ),
//         ),
//         // Play icon overlay
//         Positioned.fill(
//           child: Container(
//             decoration: BoxDecoration(
//               color: Colors.black.withValues(alpha: 0.3),
//               borderRadius: BorderRadius.circular(8),
//             ),
//             child: const Center(
//               child: Icon(
//                 Icons.play_circle_filled,
//                 color: Colors.white,
//                 size: 40,
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildImagePreview(double size) {
//     if (kIsWeb) {
//       return _fileBytes != null
//           ? Image.memory(
//               _fileBytes!,
//               width: size,
//               height: size,
//               fit: BoxFit.cover,
//               errorBuilder: (context, error, stackTrace) {
//                 return Container(
//                   width: size,
//                   height: size,
//                   color: Colors.grey.shade300,
//                   child: const Center(
//                     child: Icon(Icons.broken_image, color: Colors.grey, size: 30),
//                   ),
//                 );
//               },
//             )
//           : Container(
//               width: size,
//               height: size,
//               color: Colors.grey.shade300,
//               child: const Center(
//                 child: Icon(Icons.image, color: Colors.grey, size: 30),
//               ),
//             );
//     } else {
//       return Image.file(
//         File(widget.mediaFile.path),
//         width: size,
//         height: size,
//         fit: BoxFit.cover,
//         errorBuilder: (context, error, stackTrace) {
//           return Container(
//             width: size,
//             height: size,
//             color: Colors.grey.shade300,
//             child: const Center(
//               child: Icon(Icons.broken_image, color: Colors.grey, size: 30),
//             ),
//           );
//         },
//       );
//     }
//   }
// }

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart'; // Import NeuCard

class MediaPreviewTile extends StatefulWidget {
  final XFile mediaFile;
  final VoidCallback onRemove;

  const MediaPreviewTile({
    super.key,
    required this.mediaFile,
    required this.onRemove,
  });

  @override
  State<MediaPreviewTile> createState() => _MediaPreviewTileState();
}

class _MediaPreviewTileState extends State<MediaPreviewTile> {
  Uint8List? _videoThumbnail;
  bool _isLoading = true;
  bool _hasError = false;
  bool get _isVideo => _isVideoFile(widget.mediaFile);

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    // Simplified initializer
    try {
      if (_isVideo && !kIsWeb) {
        _videoThumbnail = await VideoThumbnail.thumbnailData(
          video: widget.mediaFile.path,
          imageFormat: ImageFormat.JPEG,
          maxWidth: 200,
          quality: 25,
        );
      }
    } catch (e) {
      debugPrint("Error initializing media preview: $e");
      if (mounted) setState(() => _hasError = true);
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  bool _isVideoFile(XFile file) {
    final path = file.path.toLowerCase();
    const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm'];
    return videoExtensions.any((ext) => path.endsWith(ext));
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          NeuCard(
            margin: EdgeInsets.zero,
            padding: EdgeInsets.zero,
            borderWidth: 2.0,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(13),
              child: SizedBox(
                width: 100,
                height: 100,
                child: _buildPreviewWidget(),
              ),
            ),
          ),
          Positioned(
            top: -5,
            right: -5,
            child: GestureDetector(
              onTap: widget.onRemove,
              child: const NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.all(2),
                backgroundColor: Color(0xFFFF6B6B),
                child: Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewWidget() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_hasError) {
      return const Center(child: Icon(Icons.error, color: Colors.red));
    }

    if (_isVideo) {
      return Stack(
        fit: StackFit.expand,
        children: [
          if (_videoThumbnail != null)
            Image.memory(_videoThumbnail!, fit: BoxFit.cover),
          Container(color: Colors.black38),
          const Center(
            child: Icon(Icons.play_circle_fill, color: Colors.white, size: 40),
          ),
        ],
      );
    } else {
      return kIsWeb
          ? Image.network(widget.mediaFile.path, fit: BoxFit.cover)
          : Image.file(File(widget.mediaFile.path), fit: BoxFit.cover);
    }
  }
}
