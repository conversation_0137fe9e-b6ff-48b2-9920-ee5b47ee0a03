import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

/// Centralized configuration service for managing API endpoints and IP addresses
class ConfigService {
  static const String _ipAddressKey = 'server_ip_address';
  static const String _portKey = 'server_port';
  static const String _defaultPort = '5000';
  static const String _defaultAndroidIp = '*************';
  static const String _defaultLocalIp = '127.0.0.1';

  static ConfigService? _instance;
  static ConfigService get instance => _instance ??= ConfigService._();
  
  ConfigService._();

  String? _cachedIpAddress;
  String? _cachedPort;

  /// Get the current server IP address
  Future<String> getServerIp() async {
    if (_cachedIpAddress != null) {
      return _cachedIpAddress!;
    }

    final prefs = await SharedPreferences.getInstance();
    final savedIp = prefs.getString(_ipAddressKey);
    
    if (savedIp != null) {
      _cachedIpAddress = savedIp;
      return savedIp;
    }

    // Default IP based on platform
    final defaultIp = defaultTargetPlatform == TargetPlatform.android
        ? _defaultAndroidIp
        : _defaultLocalIp;
    
    _cachedIpAddress = defaultIp;
    return defaultIp;
  }

  /// Get the current server port
  Future<String> getServerPort() async {
    if (_cachedPort != null) {
      return _cachedPort!;
    }

    final prefs = await SharedPreferences.getInstance();
    final savedPort = prefs.getString(_portKey);
    
    if (savedPort != null) {
      _cachedPort = savedPort;
      return savedPort;
    }

    _cachedPort = _defaultPort;
    return _defaultPort;
  }

  /// Get the complete base URL
  Future<String> getBaseUrl() async {
    final ip = await getServerIp();
    final port = await getServerPort();
    return 'http://$ip:$port';
  }

  /// Update the server IP address
  Future<void> updateServerIp(String newIp) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_ipAddressKey, newIp);
    _cachedIpAddress = newIp;
    
    if (kDebugMode) {
      print('ConfigService: Updated server IP to $newIp');
    }
  }

  /// Update the server port
  Future<void> updateServerPort(String newPort) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_portKey, newPort);
    _cachedPort = newPort;
    
    if (kDebugMode) {
      print('ConfigService: Updated server port to $newPort');
    }
  }

  /// Auto-discover the server IP address by checking common network ranges
  Future<String?> autoDiscoverServerIp({
    String port = '5000',
    Duration timeout = const Duration(seconds: 2),
  }) async {
    if (kIsWeb) {
      // Web platform can only connect to localhost
      return _defaultLocalIp;
    }

    // Get the device's current IP to determine network range
    final deviceIp = await _getDeviceIp();
    if (deviceIp == null) return null;

    final networkBase = _getNetworkBase(deviceIp);
    if (networkBase == null) return null;

    // Common IP ranges to check
    final ipRangesToCheck = [
      networkBase, // Current network range
      '192.168.1', // Common home network
      '192.168.0', // Common home network
      '192.168.8', // Your specific network
      '10.0.0',    // Common corporate network
    ];

    for (final range in ipRangesToCheck) {
      // Check common server IPs in this range
      final commonIps = [
        '$range.1',   // Router/gateway
        '$range.100', // Common server IP
        '$range.101', // Common server IP
        '$range.107', // Your specific IP
        '$range.219', // Previous IP from logs
      ];

      for (final ip in commonIps) {
        if (await _testConnection(ip, port, timeout)) {
          if (kDebugMode) {
            print('ConfigService: Found server at $ip:$port');
          }
          return ip;
        }
      }
    }

    return null;
  }

  /// Test if a server is reachable at the given IP and port
  Future<bool> _testConnection(String ip, String port, Duration timeout) async {
    try {
      final response = await http
          .get(
            Uri.parse('http://$ip:$port/api/health'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(timeout);
      
      return response.statusCode == 200;
    } catch (e) {
      // Try a simple connection test if health endpoint doesn't exist
      try {
        final socket = await Socket.connect(ip, int.parse(port))
            .timeout(timeout);
        await socket.close();
        return true;
      } catch (e) {
        return false;
      }
    }
  }

  /// Get the device's current IP address
  Future<String?> _getDeviceIp() async {
    try {
      for (var interface in await NetworkInterface.list()) {
        for (var addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
            return addr.address;
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('ConfigService: Error getting device IP: $e');
      }
    }
    return null;
  }

  /// Extract network base from IP address (e.g., "*************" -> "192.168.1")
  String? _getNetworkBase(String ip) {
    final parts = ip.split('.');
    if (parts.length >= 3) {
      return '${parts[0]}.${parts[1]}.${parts[2]}';
    }
    return null;
  }

  /// Refresh the IP address by auto-discovery and update if found
  Future<bool> refreshIpAddress() async {
    final discoveredIp = await autoDiscoverServerIp();
    if (discoveredIp != null) {
      await updateServerIp(discoveredIp);
      return true;
    }
    return false;
  }

  /// Clear cached values (useful for testing)
  void clearCache() {
    _cachedIpAddress = null;
    _cachedPort = null;
  }

  /// Reset to default values
  Future<void> resetToDefaults() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_ipAddressKey);
    await prefs.remove(_portKey);
    clearCache();
    
    if (kDebugMode) {
      print('ConfigService: Reset to default configuration');
    }
  }
}
