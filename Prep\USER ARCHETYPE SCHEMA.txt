Native Digital Products- e.g. Ebooks, digital paintings, softcopy documents like excelsheets etc.
{offering_class:string,
offering_type:string,
offering_name:string,
price:float,
price_recurring:booean(false),autogenerated,
price_recurring_frequecy:boolean(false),autogenerated,
price_recurring_fequency:int(0), autogenerated,
rating:float(aggregate, autogenerated),
tags:list,
decription:string,
reviews:[{name:string, review:string, date: datetime, rating:float, media:[image, video],
offering_images:list,
offering_videos:list,
count:int, autogenerated
}

Digitalized physical products e.g. groceries
{offering_class:string,
offering_type:string,
offering_name:string,
price:float,
price_recurring:booean(false),autogenerated,
price_recurring_frequecy:boolean(false),autogenerated,
price_recurring_fequency:int(0), autogenerated,
rating:float(aggregate, autogenerated),
tags:list,
decription:string,
reviews:[{name: string, review: string, date: datetime, rating:float, media:[image, video],
offering_images:list,
offering_videos:list,
count:int, autogenerated
}



Native Digital services e.g. video and audio editing, copywriting, 
{offering_class:string,
offering_type:string,
offering_name:string,
price:float,
price_recurring: Boolean,
price_recurring_frequecy: Boolean,
price_recurring_fequency: int,
rating:float(aggregate, autogenerated),
tags:list,
decription:string,
reviews:[{name:string, review:string, date: datetime, rating: float, media:[image, video],
offering_images:list,
offering_videos:list,
count:int(0), autogenerated
}



Digitalized services e.g. plumbing and other handyman jobs,
{offering_class:string,
offering_type:string,
offering_name:string,
price:float,
price_recurring: boolean
price_recurring_frequecy: boolean,
price_recurring_fequency: int
rating:float(aggregate, autogenerated),
tags:list,
decription:string,
reviews:[{name:string, review:string, date: datetime, rating: float, media:[image, video],
offering_images:list,
offering_videos:list,
count:int(0), autogenerated
}



