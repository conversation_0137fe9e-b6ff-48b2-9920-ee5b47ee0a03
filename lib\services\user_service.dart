import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class UserService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance; // Use the ConfigService
  final AuthService _auth = AuthService();

  /// Fetches the profile data for the currently logged-in user.
  Future<Map<String, dynamic>> getMyProfile() async {
    try {
      // --- THE FIX: Get the base URL dynamically from ConfigService ---
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.get(
        Uri.parse('$baseUrl/api/users/profile'),
      );
      // --- End of FIX ---

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load profile');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Fetches a combined list of a user's posts and created places.
  Future<List<Map<String, dynamic>>> getMyContributions() async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.get(
        Uri.parse('$baseUrl/api/users/my-contributions'),
      );
      if (response.statusCode == 200) {
        List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load contributions');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Updates the user's text-based profile information.
  Future<void> updateProfile({
    required String username,
    required String bio,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/users/profile/update'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    request.fields['username'] = username;
    request.fields['bio'] = bio;

    final response = await request.send();
    if (response.statusCode != 200) {
      throw Exception('Failed to update profile');
    }
  }

  /// Updates the user's avatar image.
  Future<void> updateAvatar(XFile image) async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/users/profile/update'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    request.files.add(
      await http.MultipartFile.fromPath('profile_pic', image.path),
    );

    final response = await request.send();
    if (response.statusCode != 200) {
      throw Exception('Failed to update avatar');
    }
  }
}
