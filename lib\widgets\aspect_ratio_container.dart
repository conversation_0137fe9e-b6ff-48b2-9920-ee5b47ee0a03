import 'package:flutter/material.dart';

class AspectRatioContainer extends StatelessWidget {
  final String imageUrl;
  final double aspectRatio;

  const AspectRatioContainer({
    super.key,
    required this.imageUrl,
    required this.aspectRatio,
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: aspectRatio > 0
          ? aspectRatio
          : 16 / 9, // Use a default if aspect ratio is invalid
      child: Image.network(imageUrl, fit: BoxFit.cover),
    );
  }
}
