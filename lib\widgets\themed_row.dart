import 'package:flutter/material.dart';
import 'item_card.dart';

class ThemedRow extends StatelessWidget {
  final String categoryTitle;
  final List<Map<String, String>> items;
  final Function(int) onItemTap; // Callback for when an item is tapped

  const ThemedRow({
    super.key,
    required this.categoryTitle,
    required this.items,
    required this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            categoryTitle,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              return GestureDetector(
                onTap: () => onItemTap(index), // Trigger the callback
                child: ItemCard(
                  imageUrl: item['imageUrl']!,
                  title: item['title']!,
                  subtitle: item['subtitle']!,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
