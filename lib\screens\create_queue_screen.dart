import 'package:flutter/material.dart';
import 'package:wicker/services/queue_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class CreateQueueScreen extends StatefulWidget {
  const CreateQueueScreen({super.key});

  @override
  _CreateQueueScreenState createState() => _CreateQueueScreenState();
}

class _CreateQueueScreenState extends State<CreateQueueScreen> {
  final _nameController = TextEditingController();
  final QueueService _queueService = QueueService();
  bool _isPrivate = true; // Queues are private by default
  bool _isLoading = false;

  void _submitQueue() async {
    if (_nameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a name for your queue')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final message = await _queueService.createQueue(
        name: _nameController.text,
        isPrivate: _isPrivate,
      );
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(message)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0), // Light beige background
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'Create New Queue',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Queue Name',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            NeuCard(
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                controller: _nameController,
                decoration: const InputDecoration(
                  hintText: 'e.g., Weekend Hangouts',
                  border: InputBorder.none,
                ),
              ),
            ),
            const SizedBox(height: 24),
            // Custom Toggle Switch
            GestureDetector(
              onTap: () => setState(() => _isPrivate = !_isPrivate),
              child: Row(
                children: [
                  NeuCard(
                    margin: EdgeInsets.zero,
                    padding: const EdgeInsets.all(4),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      alignment: _isPrivate
                          ? Alignment.centerRight
                          : Alignment.centerLeft,
                      width: 50,
                      child: NeuCard(
                        margin: EdgeInsets.zero,
                        padding: const EdgeInsets.all(8),
                        backgroundColor: _isPrivate
                            ? const Color(0xFF4ECDC4)
                            : Colors.grey.shade300,
                        shadowOffset: 0,
                        child: const SizedBox(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Make this queue private',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            // Submit Button
            GestureDetector(
              onTap: _isLoading ? null : _submitQueue,
              child: NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.all(16),
                backgroundColor: const Color(0xFF6C5CE7), // Vibrant Blue
                child: Center(
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 3,
                            color: Colors.white,
                          ),
                        )
                      : const Text(
                          'Create Queue',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
