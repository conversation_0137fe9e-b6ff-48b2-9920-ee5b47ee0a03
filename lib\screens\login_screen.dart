import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../main.dart'; // To navigate to MainScreen
import 'signup_screen.dart';
import 'network_test_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final AuthService _authService = AuthService();
  bool _isLoading = false;

  void _loginUser() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final bool loggedIn = await _authService.login(
        email: _emailController.text,
        password: _passwordController.text,
      );

      if (loggedIn && mounted) {
        // Navigate to the main app screen on successful login
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      }
    } catch (e) {
      // Show error message with more context
      if (mounted) {
        String errorMessage = e.toString().replaceFirst('Exception: ', '');

        // Provide more helpful error messages for common issues
        if (errorMessage.contains('Failed to connect to server')) {
          errorMessage = 'Cannot connect to server. Please check:\n'
              '• Your internet connection\n'
              '• Server is running\n'
              '• Both devices are on same network\n'
              '\nTap "Network Diagnostics" to test connectivity.';
        } else if (errorMessage.contains('TimeoutException')) {
          errorMessage = 'Connection timed out. The server may be unreachable.\n'
              'Please check your network connection and try again.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            duration: const Duration(seconds: 6),
            action: SnackBarAction(
              label: 'Test Network',
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const NetworkTestScreen(),
                  ),
                );
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // ... TextFields remain the same
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(labelText: 'Email'),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(labelText: 'Password'),
              obscureText: true,
            ),
            const SizedBox(height: 32),
            _isLoading
                ? const CircularProgressIndicator()
                : ElevatedButton(
                    onPressed: _loginUser,
                    child: const Text('Login'),
                  ),
            TextButton(
              onPressed: _isLoading
                  ? null
                  : () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const SignupScreen(),
                        ),
                      );
                    },
              child: const Text("Don't have an account? Sign Up"),
            ),
            const SizedBox(height: 20),
            // Debug button for network testing
            OutlinedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const NetworkTestScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.network_check, color: Colors.orange),
              label: const Text(
                'Network Diagnostics',
                style: TextStyle(color: Colors.orange),
              ),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Colors.orange),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
