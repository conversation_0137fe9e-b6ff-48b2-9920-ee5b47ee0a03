{"cells": [{"cell_type": "code", "execution_count": 10, "id": "d200da0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'_id': ObjectId('5fecd4b53d614e31c0483438'),\n", "  'SNR': '948',\n", "  'STATUS': 'AVAILABLE',\n", "  'EXPIRY': '',\n", "  'ASSIGNEE': ''}]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import pandas as pd\n", "from pymongo import MongoClient\n", "from datetime import datetime as dt\n", "from datetime import timedelta\n", "from dateutil import parser\n", "\n", "uri = \"mongodb+srv://GaudKing_Chapper:<EMAIL>/captains_log?retryWrites=true&w=majority\"\n", "conn = MongoClient(uri)\n", "\n", "# check status of the following numbers\n", "pull = conn.get_database(\"captains_log\")\n", "\n", "available = []\n", "\n", "col = pull.get_collection(name=\"snr_status\")\n", "test_list = [944,948, 947, 951, 788, 787, 766, 755, 744, 355, 358, 359, 366, 488, 476, 466, 499, 476]\n", "for i in test_list:\n", "    cursor = col.find({'SNR':str(i)})\n", "    if cursor[0]['STATUS']==\"AVAILABLE\":\n", "        available.append(cursor[0])\n", "\n", "available"]}, {"cell_type": "code", "execution_count": 11, "id": "38a42bcb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Daily Task Routing Examples ---\n", "TASK 1: Standard Numbering Application\n", "ASSIGNED TO: <PERSON> (Technical) and <PERSON> (Licensing).\n", "ACTION: Perform parallel review. <PERSON> to check NNP, <PERSON> to check licensee status and prep invoice.\n", "------------------------------\n", "TASK 2: Standard License Renewal\n", "ASSIGNED TO: <PERSON>.\n", "ACTION: Process license renewal and generate standard invoice.\n", "------------------------------\n", "TASK 3: Complex Numbering for New Service\n", "ASSIGNED TO: <PERSON>.\n", "ACTION: Analyze technical feasibility and impact on NNP. Consult with Senior Economic & Policy Analyst if needed.\n", "------------------------------\n", "TASK 4: Develop a new 5G Licensing Framework\n", "ASSIGNED TO: <PERSON> (Lead).\n", "ACTION: Form a project team to develop a new licensing framework. Report findings to Grace.\n", "------------------------------\n"]}], "source": ["# A simple representation of the team members in each role.\n", "# In a real system, this could be queried from a database.\n", "team = {\n", "    \"Technical Analyst\": \"<PERSON>\",\n", "    \"Senior Technical Analyst\": \"<PERSON>\",\n", "    \"Principal Technical Specialist\": \"<PERSON>\",\n", "    \"Licensing Analyst\": \"<PERSON>\",\n", "    \"Senior Economic & Policy Analyst\": \"Eve\",\n", "    \"Principal Economic & Policy Specialist\": \"<PERSON>\",\n", "    \"Unit Head\": \"<PERSON>\"\n", "}\n", "\n", "def route_task(task_type, complexity, details=None):\n", "    \"\"\"\n", "    Determines the correct person or sequence of people to handle a new task.\n", "\n", "    Args:\n", "        task_type (str): The category of the task. \n", "                         Expected values: 'numbering', 'licensing', 'invoicing', \n", "                                          'consultation', 'strategy'.\n", "        complexity (str): The difficulty or scope of the task.\n", "                          Expected values: 'standard', 'complex', 'strategic'.\n", "        details (dict, optional): A dictionary for extra information, \n", "                                  like {'is_new_service': True}. Defaults to None.\n", "\n", "    Returns:\n", "        str: A string describing the assignment and the initial action.\n", "    \"\"\"\n", "    if details is None:\n", "        details = {}\n", "\n", "    # --- ROUTING LOGIC ---\n", "\n", "    # 1. Standard, day-to-day tasks\n", "    if complexity == 'standard':\n", "        if task_type == 'numbering':\n", "            # As in our scenario, standard numbering requests need both technical and commercial checks.\n", "            assignee_tech = team['Technical Analyst']\n", "            assignee_license = team['Licensing Analyst']\n", "            return (f\"ASSIGNED TO: {assignee_tech} (Technical) and {assignee_license} (Licensing).\\n\"\n", "                    f\"ACTION: Perform parallel review. {assignee_tech} to check NNP, \"\n", "                    f\"{assignee_license} to check licensee status and prep invoice.\")\n", "\n", "        if task_type == 'licensing':\n", "            # e.g., a simple license renewal with no changes.\n", "            assignee = team['Licensing Analyst']\n", "            return (f\"ASSIGNED TO: {assignee}.\\n\"\n", "                    f\"ACTION: Process license renewal and generate standard invoice.\")\n", "\n", "        if task_type == 'invoicing':\n", "            # e.g., a query about a recent bill.\n", "            assignee = team['Licensing Analyst']\n", "            return (f\"ASSIGNED TO: {assignee}.\\n\"\n", "                    f\"ACTION: Address invoice query and update billing records.\")\n", "\n", "    # 2. Complex, non-standard tasks requiring senior expertise\n", "    elif complexity == 'complex':\n", "        if task_type == 'numbering':\n", "            # e.g., a request for a large, unusual block of numbers or for a new type of service.\n", "            assignee = team['Senior Technical Analyst']\n", "            return (f\"ASSIGNED TO: {assignee}.\\n\"\n", "                    f\"ACTION: Analyze technical feasibility and impact on NNP. \"\n", "                    f\"Consult with Senior Economic & Policy Analyst if needed.\")\n", "\n", "        if task_type == 'licensing':\n", "            # e.g., drafting a new license based on an existing framework but with unique conditions.\n", "            assignee = team['Senior Economic & Policy Analyst']\n", "            return (f\"ASSIGNED TO: {assignee}.\\n\"\n", "                    f\"ACTION: Draft bespoke license conditions. \"\n", "                    f\"Consult with Senior Technical Analyst on technical clauses.\")\n", "        \n", "        if task_type == 'consultation':\n", "            # e.g., running a public consultation on a draft license.\n", "            assignee = team['Senior Economic & Policy Analyst']\n", "            return (f\"ASSIGNED TO: {assignee}.\\n\"\n", "                    f\"ACTION: Manage public consultation process, collate, and analyze feedback.\")\n", "\n", "\n", "    # 3. Strategic, high-level, and future-proofing tasks\n", "    elif complexity == 'strategic':\n", "        if task_type == 'strategy' and details.get('domain') == 'technical':\n", "            # e.g., \"Review the entire National Numbering Plan for IoT readiness.\"\n", "            assignee = team['Principal Technical Specialist']\n", "            return (f\"ASSIGNED TO: {assignee} (Lead).\\n\"\n", "                    f\"ACTION: Form a project team to conduct a strategic review of the NNP. \"\n", "                    f\"Report findings to {team['Unit Head']}.\")\n", "\n", "        if task_type == 'strategy' and details.get('domain') == 'economic':\n", "            # e.g., \"Develop a new licensing framework for satellite broadband services.\"\n", "            assignee = team['Principal Economic & Policy Specialist']\n", "            return (f\"ASSIGNED TO: {assignee} (Lead).\\n\"\n", "                    f\"ACTION: Form a project team to develop a new licensing framework. \"\n", "                    f\"Report findings to {team['Unit Head']}.\")\n", "\n", "    # Default case for unhandled combinations\n", "    return f\"UNROUTED: Task type '{task_type}' with complexity '{complexity}' needs manual review by {team['Unit Head']}.\"\n", "\n", "\n", "# --- EXAMPLE USAGE ---\n", "print(\"--- Daily Task Routing Examples ---\")\n", "\n", "# Scenario 1: The example from our narrative\n", "print(\"TASK 1: Standard Numbering Application\")\n", "task1_details = route_task('numbering', 'standard')\n", "print(task1_details)\n", "print(\"-\" * 30)\n", "\n", "# Scenario 2: A simple license renewal\n", "print(\"TASK 2: Standard License Renewal\")\n", "task2_details = route_task('licensing', 'standard')\n", "print(task2_details)\n", "print(\"-\" * 30)\n", "\n", "# Scenario 3: A complex request for numbers for a new drone service\n", "print(\"TASK 3: Complex Numbering for New Service\")\n", "task3_details = route_task('numbering', 'complex', details={'is_new_service': True})\n", "print(task3_details)\n", "print(\"-\" * 30)\n", "\n", "# Scenario 4: A major strategic initiative\n", "print(\"TASK 4: Develop a new 5G Licensing Framework\")\n", "task4_details = route_task('strategy', 'strategic', details={'domain': 'economic'})\n", "print(task4_details)\n", "print(\"-\" * 30)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 5}