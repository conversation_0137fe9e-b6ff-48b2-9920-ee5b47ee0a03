import 'package:flutter/material.dart';
import 'package:wicker/services/comment_service.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/services/post_service.dart';
import 'package:wicker/widgets/comment_tile.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/post_card.dart';
import 'package:wicker/widgets/product_card.dart';

class BusinessDetailScreen extends StatefulWidget {
  final Map<String, dynamic> businessData;
  const BusinessDetailScreen({super.key, required this.businessData});

  @override
  State<BusinessDetailScreen> createState() => _BusinessDetailScreenState();
}

class _BusinessDetailScreenState extends State<BusinessDetailScreen>
    with SingleTickerProviderStateMixin {
  final EcommerceService _ecommerceService = EcommerceService();
  final PostService _postService = PostService();
  final CommentService _commentService = CommentService();
  final ConfigService _configService = ConfigService.instance;
  final TextEditingController _commentController = TextEditingController();

  late TabController _tabController;
  late Future<List<Map<String, dynamic>>> _productsFuture;
  late Future<List<Map<String, dynamic>>> _postsFuture;
  late Future<List<Map<String, dynamic>>> _commentsFuture;
  late Future<String> _baseUrlFuture; // Future to hold the base URL

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    final businessId = widget.businessData['_id']['\$oid'];
    final ownerId = widget.businessData['owner_id'] is Map
        ? widget.businessData['owner_id']['\$oid']
        : widget.businessData['owner_id'];

    _baseUrlFuture = _configService.getBaseUrl();
    _productsFuture = _ecommerceService.getBusinessProducts(businessId);
    _postsFuture = _postService.getPostsForBusiness(businessId);
    _commentsFuture = _commentService.getComments(
      parentId: businessId,
      parentType: 'business',
    );
  }

  void _postBusinessComment() async {
    if (_commentController.text.trim().isEmpty) return;
    final businessId = widget.businessData['_id']['\$oid'];
    try {
      await _commentService.postComment(
        businessId: businessId,
        commentText: _commentController.text,
      );
      _commentController.clear();
      FocusScope.of(context).unfocus();
      setState(() {
        _commentsFuture = _commentService.getComments(
          parentId: businessId,
          parentType: 'business',
        );
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to post comment: $e')));
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // --- THE FIX: All data-dependent variables are moved inside the FutureBuilder ---
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 250.0,
              floating: false,
              pinned: true,
              backgroundColor: const Color(0xFF6C5CE7),
              leading: Padding(
                padding: const EdgeInsets.all(8.0),
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: const NeuCard(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.zero,
                    child: Center(
                      child: Icon(Icons.arrow_back, color: Colors.black),
                    ),
                  ),
                ),
              ),
              flexibleSpace: FlexibleSpaceBar(
                centerTitle: true,
                titlePadding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 48,
                ),
                title: NeuCard(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  margin: EdgeInsets.zero,
                  child: Text(
                    widget.businessData['business_name'] ?? 'Business',
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                background: FutureBuilder<String>(
                  future: _baseUrlFuture,
                  builder: (context, snapshot) {
                    final List<dynamic> images =
                        widget.businessData['images'] as List<dynamic>? ?? [];
                    bool hasImage = images.isNotEmpty && snapshot.hasData;
                    String imageUrl;

                    if (hasImage) {
                      final baseUrl = snapshot.data!;
                      String imagePath = images.first;
                      imageUrl = '$baseUrl/${imagePath.replaceAll('\\', '/')}';
                    } else {
                      imageUrl =
                          'https://picsum.photos/seed/${widget.businessData['_id']['\$oid']}/800/400';
                    }

                    return Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.network(imageUrl, fit: BoxFit.cover),
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withOpacity(0.4),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ];
        },
        body: Column(
          children: [
            NeuCard(
              margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              padding: EdgeInsets.zero,
              child: TabBar(
                controller: _tabController,
                indicator: const BoxDecoration(
                  color: Color(0xFFFFE66D),
                  border: Border(
                    bottom: BorderSide(color: Colors.black, width: 3.0),
                  ),
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey.shade700,
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                tabs: const [
                  Tab(icon: Icon(Icons.shopping_cart_outlined), text: 'Shop'),
                  Tab(icon: Icon(Icons.post_add), text: 'Posts'),
                  Tab(icon: Icon(Icons.book_outlined), text: 'Guestbook'),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildShopTab(),
                  _buildPostsTab(),
                  _buildGuestbookTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShopTab() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _productsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(
            child: Text("Error loading products: ${snapshot.error}"),
          );
        }
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: NeuCard(
              backgroundColor: const Color(0xFFFFE66D),
              child: const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.storefront, size: 48),
                  SizedBox(height: 16),
                  Text(
                    'NO PRODUCTS YET',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          );
        }
        final products = snapshot.data!;
        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: products.length,
          itemBuilder: (context, index) {
            return ProductCard(productData: products[index]);
          },
        );
      },
    );
  }

  Widget _buildPostsTab() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _postsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(child: Text("Error loading posts: ${snapshot.error}"));
        }
        final posts = snapshot.data ?? [];
        if (posts.isEmpty) {
          return Center(
            child: NeuCard(
              backgroundColor: const Color(0xFFFFE66D),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.photo_library_outlined,
                    size: 48,
                    color: Colors.grey[800],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'NO POSTS YET',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          );
        }
        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: posts.length,
          itemBuilder: (context, index) {
            return PostCard(postData: posts[index]);
          },
        );
      },
    );
  }

  Widget _buildGuestbookTab() {
    return Column(
      children: [
        Expanded(
          child: FutureBuilder<List<Map<String, dynamic>>>(
            future: _commentsFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }
              if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return const Center(
                  child: Text("Be the first to leave a comment!"),
                );
              }
              final comments = snapshot.data!;
              return ListView.builder(
                itemCount: comments.length,
                itemBuilder: (context, index) {
                  return CommentTile(
                    commentData: comments[index],
                    onReply: (commentId, username) {
                      /* Reply logic can be added later */
                    },
                  );
                },
              );
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: NeuCard(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _commentController,
                    decoration: const InputDecoration(
                      hintText: "Leave a comment for this business...",
                      border: InputBorder.none,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.send),
                  onPressed: _postBusinessComment,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
