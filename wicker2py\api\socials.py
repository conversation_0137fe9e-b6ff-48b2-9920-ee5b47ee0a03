from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
import datetime

socials_bp = Blueprint('socials_bp', __name__)

# Helper to get the 'follows' collection
def get_follows_collection(db):
    return db.follows

@socials_bp.route('/follow/<user_to_follow_id>', methods=['POST'])
@jwt_required()
def follow_user(user_to_follow_id):
    """Allows the current user to follow another user."""
    db = socials_bp.db
    follows_collection = get_follows_collection(db)
    current_user_id = ObjectId(get_jwt_identity())
    
    # Prevent users from following themselves
    if current_user_id == ObjectId(user_to_follow_id):
        return jsonify({"msg": "You cannot follow yourself"}), 400

    # Check if the follow relationship already exists
    existing_follow = follows_collection.find_one({
        "follower_id": current_user_id,
        "following_id": ObjectId(user_to_follow_id)
    })

    if existing_follow:
        return jsonify({"msg": "You are already following this user"}), 409

    # Create the new follow relationship
    new_follow = {
        "follower_id": current_user_id,
        "following_id": ObjectId(user_to_follow_id),
        "created_at": datetime.datetime.now(datetime.timezone.utc)
    }
    
    try:
        follows_collection.insert_one(new_follow)
        return jsonify({"msg": "Successfully followed user"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

@socials_bp.route('/unfollow/<user_to_unfollow_id>', methods=['POST'])
@jwt_required()
def unfollow_user(user_to_unfollow_id):
    """Allows the current user to unfollow another user."""
    db = socials_bp.db
    follows_collection = get_follows_collection(db)
    current_user_id = ObjectId(get_jwt_identity())

    try:
        result = follows_collection.delete_one({
            "follower_id": current_user_id,
            "following_id": ObjectId(user_to_unfollow_id)
        })
        if result.deleted_count == 0:
            return jsonify({"msg": "You are not following this user"}), 404
            
        return jsonify({"msg": "Successfully unfollowed user"}), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500