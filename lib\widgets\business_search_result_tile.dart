import 'package:flutter/material.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class BusinessSearchResultTile extends StatelessWidget {
  final Map<String, dynamic> businessData;
  final VoidCallback onTap;

  const BusinessSearchResultTile({
    super.key,
    required this.businessData,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final String name = businessData['business_name'] ?? 'Untitled Business';
    final String description = businessData['description'] ?? 'No description.';
    final List<dynamic> images = businessData['images'] as List<dynamic>? ?? [];
    final String? imagePath = images.isNotEmpty
        ? images.first.toString()
        : null;

    return GestureDetector(
      onTap: onTap,
      child: NeuCard(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Business Image
            NeuCard(
              margin: EdgeInsets.zero,
              padding: EdgeInsets.zero,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(13),
                child: SizedBox(
                  width: 70,
                  height: 70,
                  child: imagePath != null
                      ? FutureBuilder<String>(
                          future: ConfigService.instance.getBaseUrl(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              final fullUrl =
                                  '${snapshot.data}/${imagePath.replaceAll('\\', '/')}';
                              return Image.network(fullUrl, fit: BoxFit.cover);
                            }
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          },
                        )
                      : Container(
                          color: Colors.grey.shade200,
                          child: const Icon(
                            Icons.storefront,
                            color: Colors.grey,
                          ),
                        ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            // Business Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
