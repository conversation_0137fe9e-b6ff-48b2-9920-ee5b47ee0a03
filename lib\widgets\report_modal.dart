import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:wicker/services/report_service.dart';

class ReportModal extends StatefulWidget {
  final String itemId;
  final String itemType;

  const ReportModal({super.key, required this.itemId, required this.itemType});

  @override
  State<ReportModal> createState() => _ReportModalState();
}

class _ReportModalState extends State<ReportModal> {
  final _reasonController = TextEditingController();
  final ReportService _reportService = ReportService();
  bool _isLoading = false;

  void _submit() async {
    if (_reasonController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please provide a reason for the report.'),
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      await _reportService.submitReport(
        itemId: widget.itemId,
        itemType: widget.itemType,
        reason: _reasonController.text,
      );
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Report submitted. Thank you.')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          left: 16,
          right: 16,
          top: 16,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Report Content',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            // NeuSearchBar can be used for multi-line text input as well
            NeuContainer(
              height: 120,
              color: Colors.white,
              child: TextField(
                controller: _reasonController,
                maxLines: null,
                expands: true,
                decoration: const InputDecoration(
                  hintText: 'Please provide a reason...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(8),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // THE FIX: Use GestureDetector + NeuContainer to create a custom button
            GestureDetector(
              onTap: _isLoading ? null : _submit,
              child: NeuContainer(
                height: 50,
                color: Colors.red.shade700,
                child: Center(
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Text(
                          'Submit Report',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
