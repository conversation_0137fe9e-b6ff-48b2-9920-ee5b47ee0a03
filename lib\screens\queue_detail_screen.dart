import 'package:flutter/material.dart';
import 'package:wicker/main.dart';
import 'package:wicker/services/queue_service.dart';
import 'package:wicker/widgets/place_detail_card.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:wicker/widgets/confirmation_dialog.dart';
// To navigate back to after deletion

class QueueDetailScreen extends StatefulWidget {
  final String queueId;
  final String queueName;

  const QueueDetailScreen({
    super.key,
    required this.queueId,
    required this.queueName,
  });

  @override
  State<QueueDetailScreen> createState() => _QueueDetailScreenState();
}

class _QueueDetailScreenState extends State<QueueDetailScreen> {
  final QueueService _queueService = QueueService();
  List<Map<String, dynamic>> _items = [];
  bool _isLoading = true;
  String? _tripPlan;
  LatLng _mapCenter = const LatLng(5.6037, -0.1870); // Default to Accra

  @override
  void initState() {
    super.initState();
    _fetchQueueDetails();
  }

  // NEW: Shows a modal to pick a date and time
  Future<void> _showDateTimePicker() async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (pickedDate != null && mounted) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (pickedTime != null) {
        final DateTime startDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );
        // Call the planning function with the selected date and time
        _planQueue(startDateTime);
      }
    }
  }

  Future<void> _fetchQueueDetails() async {
    setState(() => _isLoading = true);
    try {
      final data = await _queueService.getQueueDetails(widget.queueId);
      final items = List<Map<String, dynamic>>.from(
        data['populated_items'] ?? [],
      );

      if (items.isEmpty) {
        await _centerMapOnUserLocation();
      } else {
        // Center map on the first item in the queue
        final firstItemCoords = items.first['location']['coordinates'];
        _mapCenter = LatLng(firstItemCoords[1], firstItemCoords[0]);
      }

      setState(() {
        _items = items;
        _isLoading = false;
      });
    } catch (e) {
      print(e);
      setState(() => _isLoading = false);
    }
  }

  Future<void> _centerMapOnUserLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      _mapCenter = LatLng(position.latitude, position.longitude);
    } catch (e) {
      print("Could not get location: $e");
      // Keep default Accra location if it fails
    }
  }

  // Updated to accept the startDateTime
  Future<void> _planQueue(DateTime startDateTime) async {
    setState(() => _isLoading = true);
    try {
      final plan = await _queueService.planQueue(
        queueId: widget.queueId,
        startDateTime: startDateTime,
      );

      // Re-fetch the queue details to get the newly ordered items
      await _fetchQueueDetails();

      setState(() {
        _tripPlan = plan['trip_plan'];
        _isLoading = false;
      });

      if (mounted) _showTripPlanDialog();
    } catch (e) {
      // Handle error
    }
  }

  void _showTripPlanDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Your Optimized Trip Plan'),
        content: Text(_tripPlan ?? 'No plan available.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteQueue() async {
    try {
      await _queueService.deleteQueue(widget.queueId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Queue deleted successfully.')),
        );
        // Navigate back to the main Hub screen after deletion
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
            builder: (context) => const MainScreen(initialIndex: 3),
          ), // Assuming Hub is at index 3
          (Route<dynamic> route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting queue: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _removeItem(String itemId, int index) async {
    try {
      await _queueService.removeItemFromQueue(widget.queueId, itemId);
      setState(() {
        _items.removeAt(index);
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Item removed from queue.')));
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
    }
  }

  @override
  Widget build(BuildContext context) {
    final markers = _items.map((item) {
      final coords = item['location']['coordinates'];
      return Marker(
        point: LatLng(coords[1], coords[0]),
        child: const Icon(Icons.location_pin, color: Colors.red),
      );
    }).toList();
    final points = _items.map((item) {
      final coords = item['location']['coordinates'];
      return LatLng(coords[1], coords[0]);
    }).toList();

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.queueName),
        actions: [
          IconButton(
            icon: const Icon(EvaIcons.trash2Outline),
            onPressed: () {
              // Show the confirmation dialog
              showDialog(
                context: context,
                builder: (context) => ConfirmationDialog(
                  title: 'Delete Queue?',
                  content:
                      'Are you sure you want to permanently delete this queue?',
                  onConfirm: _deleteQueue,
                ),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                SizedBox(
                  height: 250,
                  child: FlutterMap(
                    options: MapOptions(
                      initialCenter: _mapCenter,
                      initialZoom: 12,
                    ),
                    children: [
                      TileLayer(
                        urlTemplate:
                            'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                      ),
                      // THE FIX: Only build these layers if the list is not empty
                      if (points.isNotEmpty)
                        PolylineLayer(
                          polylines: [
                            Polyline(
                              points: points,
                              color: Colors.blue,
                              strokeWidth: 3,
                            ),
                          ],
                        ),
                      if (markers.isNotEmpty) MarkerLayer(markers: markers),
                    ],
                  ),
                ),
                Expanded(
                  child: ReorderableListView.builder(
                    itemCount: _items.length,
                    itemBuilder: (context, index) {
                      final item = _items[index];
                      final itemId = item['_id']['\$oid'];

                      return Dismissible(
                        key: ValueKey(itemId),
                        direction: DismissDirection.endToStart,
                        onDismissed: (_) => _removeItem(itemId, index),
                        background: Container(
                          color: Colors.red.shade700,
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: const Icon(
                            EvaIcons.trash2,
                            color: Colors.white,
                          ),
                        ),
                        child: PlaceDetailCard(placeData: item, onClose: () {}),
                      );
                    },
                    onReorder: (oldIndex, newIndex) {
                      setState(() {
                        if (newIndex > oldIndex) newIndex -= 1;
                        final item = _items.removeAt(oldIndex);
                        _items.insert(newIndex, item);
                      });
                    },
                  ),
                ),
              ],
            ),
      floatingActionButton: NeuTextButton(
        enableAnimation: true,
        onPressed: _items.isEmpty
            ? null
            : _showDateTimePicker, // Call the date picker
        buttonColor: _items.isEmpty ? Colors.grey : Colors.teal,
        text: const Text(
          'Plan My Queue',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}
