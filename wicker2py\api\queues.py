import json
from flask import Response, current_app, request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime

queues_bp = Blueprint('queues_bp', __name__)

@queues_bp.route('/create', methods=['POST'])
@jwt_required()
def create_queue():
    db = queues_bp.db
    queues_collection = db.queues
    current_user_id = get_jwt_identity()
    data = request.get_json()

    if not data or not data.get('name'):
        return jsonify({"msg": "Queue name is required"}), 400

    new_queue = {
        "name": data.get('name'),
        "is_private": data.get('is_private', True),
        "owner_id": ObjectId(current_user_id),
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "items": [] # Renamed from 'locations' to be consistent
    }

    try:
        queues_collection.insert_one(new_queue)
        return jsonify({"msg": "Queue created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

# FIX: Renamed function to get_my_queues
@queues_bp.route('/', methods=['GET'])
@jwt_required()
def get_my_queues():
    db = queues_bp.db
    queues_collection = db.queues
    current_user_id = get_jwt_identity()

    try:
        user_queues = list(queues_collection.find({
            "owner_id": ObjectId(current_user_id)
        }))
        return Response(
            json_util.dumps(user_queues),
            mimetype='application/json'
        )
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    



@queues_bp.route('/<queue_id>/add-item', methods=['POST'])
@jwt_required()
def add_item_to_queue(queue_id):
    db = queues_bp.db
    queues_collection = db.queues
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    item_id = data.get('item_id')
    item_type = data.get('item_type')

    if not item_id or not item_type:
        return jsonify({"msg": "item_id and item_type are required"}), 400

    # Find the queue and ensure the current user owns it
    queue_to_update = {
        '_id': ObjectId(queue_id),
        'owner_id': current_user_id
    }

    # The new item to be added
    new_item = {
        'item_id': ObjectId(item_id),
        'item_type': item_type,
        'added_at': datetime.datetime.now(datetime.timezone.utc)
    }

    # Update the found queue by pushing the new item
    result = queues_collection.update_one(
        queue_to_update,
        {'$push': {'items': new_item}}
    )

    if result.modified_count == 0:
        return jsonify({"msg": "Queue not found or you don't have permission"}), 404

    return jsonify({"msg": "Item added to queue"}), 200





@queues_bp.route('/<queue_id>', methods=['GET'])
@jwt_required()
def get_queue_details(queue_id): # FIX: Renamed function
    db = queues_bp.db
    queues_collection = db.queues # FIX: Added this line


    try:
        # First check if the queue exists
        queue = queues_collection.find_one({'_id': ObjectId(queue_id)})
        if not queue:
            return jsonify({"msg": "Queue not found"}), 404

        # If queue has no items, return the basic queue info
        if not queue.get('items') or len(queue['items']) == 0:
            return Response(json_util.dumps({
                '_id': queue['_id'],
                'name': queue['name'],
                'owner_id': queue['owner_id'],
                'is_private': queue['is_private'],
                'created_at': queue.get('created_at'),
                'items': [],
                'populated_items': []
            }), mimetype='application/json')

        # This pipeline finds the queue and then "looks up" the full
        # documents for the items (posts and places) it contains.
        pipeline = [
            {'$match': {'_id': ObjectId(queue_id)}},
            {'$unwind': {'path': '$items', 'preserveNullAndEmptyArrays': True}},
            {
                '$lookup': {
                    'from': 'posts',
                    'localField': 'items.item_id',
                    'foreignField': '_id',
                    'as': 'populated_post'
                }
            },
            {
                '$lookup': {
                    'from': 'places',
                    'localField': 'items.item_id',
                    'foreignField': '_id',
                    'as': 'populated_place'
                }
            },
            {
                '$addFields': {
                    'populated_item': {'$ifNull': [{'$arrayElemAt': ['$populated_post', 0]}, {'$arrayElemAt': ['$populated_place', 0]}]}
                }
            },
            {
                '$group': {
                    '_id': '$_id',
                    'name': {'$first': '$name'},
                    'owner_id': {'$first': '$owner_id'},
                    'is_private': {'$first': '$is_private'},
                    'created_at': {'$first': '$created_at'},
                    'items': {'$push': '$items'},
                    'populated_items': {'$push': '$populated_item'}
                }
            }
        ]

        result = list(queues_collection.aggregate(pipeline))

        if not result:
            return jsonify({"msg": "Queue not found"}), 404

        return Response(json_util.dumps(result[0]), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    


@queues_bp.route('/<queue_id>/plan', methods=['POST'])
@jwt_required()
def plan_queue_route(queue_id):
    """
    Uses an OpenAI agent to create an optimized itinerary for the places in a queue,
    based on a user-provided start date and time.
    """
    db = queues_bp.db
    openai_client = current_app.openai_client
    queues_collection = db.queues
    data = request.get_json()

    # 1. Get the start date and time from the request body
    start_datetime = data.get('start_datetime')
    if not start_datetime:
        return jsonify({"msg": "A start date and time are required."}), 400



    # First, find the queue to ensure it exists.
    queue = queues_collection.find_one({'_id': ObjectId(queue_id)})

    if not queue:
        return jsonify({"msg": "Queue not found"}), 404

    # Check if queue has items
    if not queue.get('items') or len(queue['items']) == 0:
        return jsonify({"msg": "This queue is empty, nothing to plan."}), 400

    # Fetch the queue details using aggregation to populate places
    pipeline = [
        {'$match': {'_id': ObjectId(queue_id)}},
        {'$unwind': {'path': '$items', 'preserveNullAndEmptyArrays': True}},
        {'$lookup': {'from': 'places', 'localField': 'items.item_id', 'foreignField': '_id', 'as': 'populated_place'}},
        {'$addFields': {'populated_item': {'$arrayElemAt': ['$populated_place', 0]}}},
        {'$match': {'populated_item': {'$ne': None}}},  # Only include items that are places
        {'$group': {'_id': '$_id', 'name': {'$first': '$name'}, 'populated_items': {'$push': '$populated_item'}}}
    ]
    result = list(db.queues.aggregate(pipeline))

    if not result or not result[0].get('populated_items'):
        return jsonify({"msg": "This queue has no places to plan."}), 400

    places = result[0]['populated_items']

    # --- Agentic Workflow ---
    places_text = ""
    for i, place in enumerate(places):
        opening_hours = place.get('opening_hours', 'Not specified')
        category = place.get('category', 'General')
        places_text += f"{i+1}. {place.get('name')} (Category: {category}, Hours: {opening_hours})\n"

    # 2. Update the prompt to use the new start_datetime variable
    prompt = f"""
    You are an expert tour guide for Accra, Ghana. Given the following list of places, create an optimized one-day itinerary.
    The user wants to start their trip on: {start_datetime}.

    Consider logical factors like typical opening times for the category of place (e.g., a coffee shop in the morning, a restaurant for lunch, a nightclub in the evening) and try to create a realistic schedule. The user is located in Accra.

    Here is the list of places:
    {places_text}

    Your response must be in two parts:
    1. "plan": A summarized, step-by-step trip plan with timings and brief reasons for the order.
    2. "order": A comma-separated list of the original numbers of the places in the new, optimized order (e.g., "3,1,2,4").

    Format your response as a JSON object.
    """

    try:
        # 3. Call the OpenAI API with the new prompt
        completion = openai_client.chat.completions.create(
            model="gpt-4-turbo",
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": "You are a helpful tour guide assistant that provides responses in JSON format."},
                {"role": "user", "content": prompt}
            ]
        )
   
        response_data = json.loads(completion.choices[0].message.content)

        # 4. Reorder the original places list based on the AI's response
        optimized_order_indices = [int(i.strip()) - 1 for i in response_data.get('order', '').split(',')]
        ordered_places = [places[i] for i in optimized_order_indices if i < len(places)]
        print(f"Ordered places: {ordered_places}")
        return jsonify({
            "trip_plan": response_data.get('plan'),
            "ordered_places": json.loads(json_util.dumps(ordered_places))
        }), 200


    except Exception as e:
        print(f"Error in plan_queue_route: {e}")
        return jsonify({"msg": "Failed to generate trip plan from AI", "error": str(e)}), 500



@queues_bp.route('/<queue_id>/items/<item_id>', methods=['DELETE'])
@jwt_required()
def remove_item_from_queue(queue_id, item_id):
    """
    Remove an item from a queue.
    """
    db = queues_bp.db
    queues_collection = db.queues
    current_user_id = get_jwt_identity()

    try:
        # Verify the queue exists and user owns it
        queue = queues_collection.find_one({
            '_id': ObjectId(queue_id),
            'owner_id': ObjectId(current_user_id)
        })

        if not queue:
            return jsonify({"msg": "Queue not found or access denied"}), 404

        # Remove the item from the queue
        result = queues_collection.update_one(
            {'_id': ObjectId(queue_id)},
            {'$pull': {'items': {'item_id': ObjectId(item_id)}}}
        )

        if result.modified_count > 0:
            return jsonify({"msg": "Item removed from queue successfully"}), 200
        else:
            return jsonify({"msg": "Item not found in queue"}), 404

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@queues_bp.route('/<queue_id>/delete', methods=['POST']) 
@jwt_required()
def delete_queue(queue_id):
    """
    Delete a queue.
    """
    db = queues_bp.db
    queues_collection = db.queues
    current_user_id = get_jwt_identity()

    try:
        # Verify the queue exists and user owns it
        result = queues_collection.delete_one({
            '_id': ObjectId(queue_id),
            'owner_id': ObjectId(current_user_id)
        })

        if result.deleted_count > 0:
            return jsonify({"msg": "Queue deleted successfully"}), 200
        else:
            return jsonify({"msg": "Queue not found or access denied"}), 404

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500