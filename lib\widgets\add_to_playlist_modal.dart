import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:wicker/services/playlist_service.dart';
import 'package:wicker/services/queue_service.dart';

class AddToPlaylistModal extends StatefulWidget {
  final String itemId;
  final String itemType; // 'post' or 'place'

  const AddToPlaylistModal({
    super.key,
    required this.itemId,
    required this.itemType,
  });

  @override
  _AddToPlaylistModalState createState() => _AddToPlaylistModalState();
}

class _AddToPlaylistModalState extends State<AddToPlaylistModal> {
  final _customNameController = TextEditingController();
  final PlaylistService _playlistService = PlaylistService();
  final QueueService _queueService = QueueService();

  // Combined list of playlists and queues
  List<Map<String, dynamic>> _allLists = [];
  Map<String, dynamic>? _selectedList;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchUserLists();
  }

  Future<void> _fetchUserLists() async {
    try {
      final playlists = await _playlistService.getMyPlaylists();
      final queues = await _queueService.getMyQueues();

      setState(() {
        // Add a 'type' field to differentiate them
        _allLists = [
          ...playlists.map((p) => {...p, 'list_type': 'playlist'}),
          ...queues.map((q) => {...q, 'list_type': 'queue'}),
        ];
        _isLoading = false;
      });
    } catch (e) {
      // Handle error
    }
  }

// In lib/widgets/add_to_playlist_modal.dart -> _AddToPlaylistModalState

void _saveItem() async {
  if (_selectedList == null) return;

  final listId = _selectedList!['_id']['\$oid'];
  final listType = _selectedList!['list_type'];
  final customName = _customNameController.text;

  // Disable the button to prevent multiple submissions
  setState(() {});

  try {
    String message = '';
    if (listType == 'playlist') {
      await _playlistService.addItemToPlaylist(
        playlistId: listId,
        itemId: widget.itemId,
        itemType: widget.itemType,
        customName: customName.isNotEmpty ? customName : null,
      );
      message = 'Saved to playlist';
    } else if (listType == 'queue') {
      // THE FIX: Call the addItemToQueue service
      await _queueService.addItemToQueue(
        queueId: listId,
        itemId: widget.itemId,
        itemType: widget.itemType,
      );
      message = 'Added to queue';
    }

    if(mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('$message: ${_selectedList!['name']}')),
        );
    }
  } catch (e) {
    if(mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${e.toString()}')),
        );
    }
  }
}
  @override
  Widget build(BuildContext context) {
    return Padding(
      // THE FIX: Add padding to account for the keyboard
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(20.0),
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Save to...', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                DropdownButtonFormField<Map<String, dynamic>>(
                  value: _selectedList,
                  hint: const Text('Choose a list'),
                  onChanged: (value) => setState(() => _selectedList = value),
                  items: _allLists.map((list) {
                    return DropdownMenuItem(
                      value: list,
                      child: Text(list['name']),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
            NeuSearchBar(
              searchController: _customNameController,
              hintText: 'Save as (optional)',
              keyboardType: TextInputType.text,
              searchBarHeight: 60,
            ),
                const SizedBox(height: 24),
                NeuTextButton(
                  enableAnimation: true,
                  onPressed: _saveItem,
                  buttonColor: Colors.teal,
                  text: const Text('Add to List', style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
    ),
      ),
    );
  }
}