import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/places_service.dart';

class EcommerceService {
  final ConfigService _config = ConfigService.instance;
  final AuthService _auth = AuthService();
  final WickerHttpClient _client = WickerHttpClient();

  // --- NEW: Adds a product to the user's business ---
  Future<void> addProduct({
    required String businessId,
    required String productName,
    required String description,
    required double price,
    required String productType,
    String? barcode,
    int stockCount = 1,
    required List<XFile> media,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();
    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/ecommerce/product/add'),
    );
    request.headers['Authorization'] = 'Bearer $token';

    // Add fields
    request.fields['business_id'] = businessId;
    request.fields['product_name'] = productName;
    request.fields['description'] = description;
    request.fields['price'] = price.toString();
    request.fields['product_type'] = productType;
    if (barcode != null) {
      request.fields['barcode'] = barcode;
    }
    request.fields['stock_count'] = stockCount.toString();

    // Add media files
    for (var file in media) {
      request.files.add(await http.MultipartFile.fromPath('media', file.path));
    }

    final response = await request.send();
    if (response.statusCode != 201) {
      throw Exception('Failed to add product');
    }
  }

  Future<Map<String, dynamic>?> getMyBusiness() async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();

    final response = await http.get(
      Uri.parse('$baseUrl/api/ecommerce/business/my-business'),
      headers: {'Authorization': 'Bearer $token'},
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    if (response.statusCode == 404) {
      return null;
    }
    throw Exception('Failed to fetch business data');
  }

  Future<Map<String, dynamic>> getBusinessDetails(String businessId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/ecommerce/business/$businessId'),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    throw Exception('Failed to load business details');
  }

  // --- NEW: Gets a business by the owner's user ID ---
  Future<Map<String, dynamic>?> getBusinessByOwner(String ownerId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/ecommerce/business/owner/$ownerId'),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    if (response.statusCode == 404) {
      return null; // It's not an error if the user simply doesn't have a business
    }
    throw Exception('Failed to fetch business data');
  }

  // --- NEW: Gets all products for a given business ID ---
  Future<List<Map<String, dynamic>>> getBusinessProducts(
    String businessId,
  ) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/ecommerce/business/$businessId/products'),
    );
    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data);
    }
    throw Exception('Failed to load products');
  }

  // --- THE FIX: Removed the unused positional 'text' parameter ---
  Future<void> createBusiness({
    required String businessName,
    required String description,
    required List<XFile> images,
  }) async {
    // --- End of FIX ---
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/ecommerce/business/create'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    request.fields['business_name'] = businessName;
    request.fields['description'] = description;

    for (var image in images) {
      request.files.add(
        await http.MultipartFile.fromPath('images', image.path),
      );
    }

    final response = await request.send();
    if (response.statusCode != 201) {
      throw Exception('Failed to create business');
    }
  }
}
