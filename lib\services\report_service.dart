import 'dart:convert';
import 'package:wicker/services/places_service.dart'; // Using WickerHttpClient

class ReportService {
  final WickerHttpClient _client = WickerHttpClient();
  final String _baseUrl = "http://*************:5000"; // Adjust IP as needed

  Future<void> submitReport({
    required String itemId,
    required String itemType,
    required String reason,
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/api/reports/create'),
        body: jsonEncode({
          'item_id': itemId,
          'item_type': itemType,
          'reason': reason,
        }),
      );

      if (response.statusCode != 201) {
        throw Exception('Failed to submit report');
      }
    } catch (e) {
      rethrow;
    }
  }
}
