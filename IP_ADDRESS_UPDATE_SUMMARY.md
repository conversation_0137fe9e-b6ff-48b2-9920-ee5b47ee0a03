# IP Address Update Summary

## Latest Update: 2025-08-01T15:27:13.232425

**Current IP Address: ***************

## Overview

Updated all IP addresses throughout the Flutter application to use `http://*************:5000` for Android physical device testing, while maintaining `http://127.0.0.1:5000` for web and iOS platforms.

## Auto-Update Script

This project includes an automatic IP update script:

```bash
# Auto-discover and update IP
dart scripts/update_ip.dart

# Update to specific IP
dart scripts/update_ip.dart *************
```

## Platform Detection Strategy

All services use the following pattern for cross-platform compatibility:

```dart
final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
    ? "http://*************:5000"  // Physical Android device
    : "http://127.0.0.1:5000";      // Web/iOS/Desktop
```

## Network Configuration

The app is configured to work with:
- **Android Physical Device**: `http://*************:5000`
- **Web/iOS/Desktop**: `http://127.0.0.1:5000`
- **Android Manifest**: Already configured with `android:usesCleartextTraffic="true"` for HTTP traffic

## Files Updated by Script

- lib/services/auth_service.dart
- lib/services/post_service.dart
- lib/services/places_service.dart
- lib/services/queue_service.dart
- lib/services/playlist_service.dart
- lib/screens/home_screen.dart
- lib/screens/explore_screen.dart
- lib/widgets/post_card.dart
- lib/widgets/place_detail_card.dart
- lib/widgets/user_contributions_tab.dart

## Next Steps for Testing

1. Ensure the backend server is running on `*************:5000`
2. Connect Android device to the same network
3. Build and install the app on the physical device:
   ```bash
   flutter build apk --debug
   flutter install
   ```
4. Test all network-dependent features (authentication, posts, places, etc.)

---
*This document was automatically updated by the IP update script.*
