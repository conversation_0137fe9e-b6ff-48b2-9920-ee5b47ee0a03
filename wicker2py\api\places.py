import json
import os
from flask import Response, request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime
from werkzeug.utils import secure_filename
import uuid

places_bp = Blueprint('places_bp', __name__)

def get_db_collections(db):
    places_collection = db.places
    users_collection = db.users
    return places_collection, users_collection

# --- THE FIX: Updated the main route handler ---
@places_bp.route('/', methods=['GET', 'POST']) # 1. Allow POST requests
@jwt_required()
def handle_places():
    db = places_bp.db
    # 2. Check which HTTP method was used
    if request.method == 'POST':
        return add_place(db) # 3. Call the add_place function
    else: # Default to GET
        return get_explore_content(db)

# Note: The function now accepts 'db' as an argument
def add_place(db):
    places_collection, users_collection = get_db_collections(db)
    current_user_id = get_jwt_identity()
    data = request.form

    if not data or not data.get('name') or not data.get('location'):
        return jsonify({"msg": "Missing required fields: name, location"}), 400
    
    # --- Image Handling ---
    uploaded_photo_paths = []
    if 'images' in request.files:
        images = request.files.getlist('images')
        for image in images:
            if image.filename != '':
                filename = secure_filename(image.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                upload_folder = 'uploads' 
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)
                
                image_path = os.path.join(upload_folder, unique_filename)
                
                image.save(image_path)
                uploaded_photo_paths.append(image_path)

    # --- Gamification Points ---
    points_awarded = 0
    if data.get('name'): points_awarded += 10
    if data.get('category'): points_awarded += 10
    if data.get('review'): points_awarded += 5
    if data.get('rating'): points_awarded += 5
    if uploaded_photo_paths: points_awarded += len(uploaded_photo_paths) * 2

    location_data = json.loads(data.get('location'))

    # --- Document Creation ---
    new_place = {
        "name": data.get('name'),
        "category": data.get('category'),
        "location": {
            "type": "Point",
            "coordinates": [float(location_data['longitude']), float(location_data['latitude'])]
        },
        "photos": uploaded_photo_paths,
        "created_by": ObjectId(current_user_id),
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "reviews": [],
        "overall_rating": 0,
        "likes": [], # Add likes array for new places
        "dislikes": [] # Add dislikes array for new places
    }

    if data.get('review') or data.get('rating'):
        initial_review = {
            "user_id": ObjectId(current_user_id),
            "rating": float(data.get('rating', 0)),
            "review_text": data.get('review', ""),
            "created_at": datetime.datetime.now(datetime.timezone.utc)
        }
        new_place["reviews"].append(initial_review)
        new_place["overall_rating"] = float(data.get('rating', 0))

    try:
        places_collection.insert_one(new_place)
        users_collection.update_one(
            {'_id': ObjectId(current_user_id)},
            {'$inc': {'points': points_awarded}}
        )
        return jsonify({"msg": f"Place added! You earned {points_awarded} points."}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    




# In api/places.py

# This function replaces get_all_places
# def get_explore_content(db):
#     """Fetches a combined list of all public posts and places for the Explore page."""
#     posts_collection = db.posts
#     places_collection = db.places

#     # Get all posts
#     posts_pipeline = [
#         {'$addFields': {'content_type': 'post'}},
#         # You can add more stages here, like lookups for author details
#     ]
#     all_posts = list(posts_collection.aggregate(posts_pipeline))

#     # Get all places
#     places_pipeline = [
#         {'$addFields': {'content_type': 'place'}},
#     ]
#     all_places = list(places_collection.aggregate(places_pipeline))

#     # Combine, sort, and return
#     combined_content = sorted(all_posts + all_places, key=lambda x: x['created_at'], reverse=True)
#     return Response(json_util.dumps(combined_content), mimetype='application/json')

# --- THE FIX: This function now attaches full author_details ---
def get_explore_content(db):
    """Fetches a combined list of all public posts and places, ensuring
    author_details are included for every item."""
    posts_collection = db.posts
    places_collection = db.places

    # Pipeline to fetch and format posts with author details
    posts_pipeline = [
        {'$lookup': {
            'from': 'users', 'localField': 'author_id',
            'foreignField': '_id', 'as': 'author_details'
        }},
        {'$unwind': '$author_details'},
        {'$addFields': {'content_type': 'post'}}
    ]
    all_posts = list(posts_collection.aggregate(posts_pipeline))

    # Pipeline to fetch and format places with author details
    places_pipeline = [
        {'$lookup': {
            'from': 'users', 'localField': 'created_by',
            'foreignField': '_id', 'as': 'author_details'
        }},
        {'$unwind': '$author_details'},
        {'$addFields': {'content_type': 'place'}}
    ]
    all_places = list(places_collection.aggregate(places_pipeline))

    # Combine, sort, and return
    combined_content = sorted(all_posts + all_places, key=lambda x: x['created_at'], reverse=True)
    return Response(json_util.dumps(combined_content), mimetype='application/json')
# --- End of FIX ---


    


# --- NEW: Endpoint to claim a place as a business ---
@places_bp.route('/<place_id>/claim', methods=['POST'])
@jwt_required()
def claim_place(place_id):
    """Allows a user to claim a place they created, linking it to their business."""
    db = places_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    # 1. Find the user's business
    business = db.businesses.find_one({"owner_id": current_user_id})
    if not business:
        return jsonify({"msg": "You must create a business profile first"}), 404

    # 2. Find the place and verify the current user is the creator
    place = db.places.find_one({
        "_id": ObjectId(place_id),
        "created_by": current_user_id
    })
    if not place:
        return jsonify({"msg": "Place not found or you are not the creator"}), 404

    # 3. Link the place to the business
    db.places.update_one(
        {"_id": ObjectId(place_id)},
        {"$set": {"business_id": business['_id']}}
    )
    
    return jsonify({"msg": "Place successfully claimed and linked to your business"}), 200



# In api/places.py

@places_bp.route('/<place_id>', methods=['GET'])
@jwt_required()
def get_place_details(place_id):
    """
    Fetches the details for a single place, and also finds all posts
    that have been tagged with this place's ID.
    """

    db = places_bp.db
    places_collection = db.places
    current_user_id = ObjectId(get_jwt_identity())
    
    try:
        pipeline = [
            {'$match': {'_id': ObjectId(place_id)}},
            {
                '$lookup': {
                    'from': 'posts',
                    'localField': '_id',
                    'foreignField': 'tagged_place_id',
                    'as': 'tagged_posts',
                    # Add a sub-pipeline to check for likes/dislikes
                    'pipeline': [
                        {
                            '$addFields': {
                                'isLiked': {'$in': [current_user_id, {'$ifNull': ['$likes', []]}]},
                                'isDisliked': {'$in': [current_user_id, {'$ifNull': ['$dislikes', []]}]}
                            }
                        }
                    ]
                }
            }
        ]
        
        result = list(places_collection.aggregate(pipeline))
        
        if not result:
            return jsonify({"msg": "Place not found"}), 404
        print('results', result)
        return Response(json_util.dumps(result[0]), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    


@places_bp.route('/<place_id>/like', methods=['POST'])
@jwt_required()
def like_place(place_id):
    db = places_bp.db
    places_collection = db.places
    current_user_id = ObjectId(get_jwt_identity())
    place_object_id = ObjectId(place_id)

    # Remove user from dislikes if they are switching their vote
    places_collection.update_one(
        {'_id': place_object_id},
        {'$pull': {'dislikes': current_user_id}}
    )

    # Add user to likes (if not already there)
    places_collection.update_one(
        {'_id': place_object_id},
        {'$addToSet': {'likes': current_user_id}}
    )
    return jsonify({"msg": "place liked"}), 200

@places_bp.route('/<place_id>/dislike', methods=['POST'])
@jwt_required()
def dislike_place(place_id):
    db = places_bp.db
    places_collection = db.places
    current_user_id = ObjectId(get_jwt_identity())
    place_object_id = ObjectId(place_id)

    # Remove user from likes if they are switching their vote
    places_collection.update_one(
        {'_id': place_object_id},
        {'$pull': {'likes': current_user_id}}
    )

    # Add user to dislikes (if not already there)
    places_collection.update_one(
        {'_id': place_object_id},
        {'$addToSet': {'dislikes': current_user_id}}
    )
    return jsonify({"msg": "place disliked"}), 200