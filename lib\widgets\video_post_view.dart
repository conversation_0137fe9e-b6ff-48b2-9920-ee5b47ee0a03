import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class VideoPostView extends StatefulWidget {
  final String videoUrl;
  const VideoPostView({super.key, required this.videoUrl});

  @override
  _VideoPostViewState createState() => _VideoPostViewState();
}

class _VideoPostViewState extends State<VideoPostView> {
  late VideoPlayerController _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller and start playing the video
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl))
      ..initialize().then((_) {
        setState(() {}); // Ensure the first frame is shown
        _controller.play();
        _controller.setLooping(true); // Loop the video
      });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      child: Center(
        child: _controller.value.isInitialized
            ? AspectRatio(
                aspectRatio: _controller.value.aspectRatio,
                child: VideoPlayer(_controller),
              )
            : const CircularProgressIndicator(),
      ),
    );
  }

  @override
  void dispose() {
    // Dispose the controller to free up resources
    _controller.dispose();
    super.dispose();
  }
}
