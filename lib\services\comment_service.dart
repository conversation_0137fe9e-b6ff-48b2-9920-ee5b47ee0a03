import 'dart:convert';
import 'package:wicker/services/places_service.dart';
import 'package:wicker/services/config_service.dart';

class CommentService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  // REFACTORED: Now requires parentId and parentType
  Future<List<Map<String, dynamic>>> getComments({
    required String parentId,
    required String parentType, // 'post' or 'business'
    String? searchQuery,
    String? sentiment,
  }) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final uri = Uri.parse('$baseUrl/api/comments/$parentId').replace(
        queryParameters: {
          'type': parentType, // Pass the type to the backend
          if (searchQuery != null && searchQuery.isNotEmpty)
            'search': searchQuery,
          if (sentiment != null && sentiment != 'all') 'sentiment': sentiment,
        },
      );
      final response = await _client.get(uri);
      if (response.statusCode == 200) {
        List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load comments');
      }
    } catch (e) {
      rethrow;
    }
  }

  // REFACTORED: Can now post a comment to a post OR a business
  Future<void> postComment({
    String? postId,
    String? businessId,
    required String commentText,
  }) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final body = {'comment_text': commentText};
      if (postId != null) {
        body['post_id'] = postId;
      } else if (businessId != null) {
        body['business_id'] = businessId;
      } else {
        throw Exception("Either postId or businessId must be provided.");
      }

      final response = await _client.post(
        Uri.parse('$baseUrl/api/comments/create'),
        body: jsonEncode(body),
      );
      if (response.statusCode != 201) {
        throw Exception('Failed to post comment');
      }
    } catch (e) {
      rethrow;
    }
  }
}
