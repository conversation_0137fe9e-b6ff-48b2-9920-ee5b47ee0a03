import 'package:flutter/material.dart';
import 'package:wicker/services/playlist_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class CreatePlaylistScreen extends StatefulWidget {
  const CreatePlaylistScreen({super.key});

  @override
  _CreatePlaylistScreenState createState() => _CreatePlaylistScreenState();
}

class _CreatePlaylistScreenState extends State<CreatePlaylistScreen> {
  final _nameController = TextEditingController();
  final PlaylistService _playlistService = PlaylistService();
  bool _isPrivate = false;
  bool _isLoading = false;

  void _submitPlaylist() async {
    if (_nameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a name for your playlist')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final message = await _playlistService.createPlaylist(
        name: _nameController.text,
        isPrivate: _isPrivate,
      );
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(message)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0), // Light beige background
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'Create Playlist',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Playlist Name',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            NeuCard(
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                controller: _nameController,
                decoration: const InputDecoration(
                  hintText: 'e.g., Accra Street Food Tour',
                  border: InputBorder.none,
                ),
              ),
            ),
            const SizedBox(height: 24),
            // Custom Toggle Switch
            GestureDetector(
              onTap: () => setState(() => _isPrivate = !_isPrivate),
              child: Row(
                children: [
                  NeuCard(
                    margin: EdgeInsets.zero,
                    padding: const EdgeInsets.all(4),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      alignment: _isPrivate
                          ? Alignment.centerRight
                          : Alignment.centerLeft,
                      width: 50,
                      child: NeuCard(
                        margin: EdgeInsets.zero,
                        padding: const EdgeInsets.all(8),
                        backgroundColor: _isPrivate
                            ? const Color(0xFF4ECDC4)
                            : Colors.grey.shade300,
                        shadowOffset: 0,
                        child: const SizedBox(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Make this playlist private',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            // Submit Button
            GestureDetector(
              onTap: _isLoading ? null : _submitPlaylist,
              child: NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.all(16),
                backgroundColor: const Color(0xFF6C5CE7), // Vibrant Blue
                child: Center(
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 3,
                            color: Colors.white,
                          ),
                        )
                      : const Text(
                          'Create Playlist',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
