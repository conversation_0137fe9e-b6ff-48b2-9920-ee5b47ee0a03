# Step-by-Step To-Do List: Accra Local Discovery, Commerce & Delivery App

This checklist outlines tasks for the MVP and subsequent major feature additions, including the Secure Delivery Platform.

## Phase 0: Setup & Planning (Overall Project)


* [ ] **Design & Architecture (Overall Project):**
    * [ ] Create detailed UI/UX wireframes and mockups for all MVP screens (Ref PRD 3.1-3.10), exploring hybrid Brutalism/Glassmorphism.
    * [ ] Develop interactive prototypes for key MVP user flows.
    * [ ] **Initial Database Schema Design (MVP focus, but consider future needs like delivery). Make preliminary decision/investigation on SQL vs. NoSQL (e.g., MongoDB for geospatial).**
    * [ ] Define detailed API specification for MVP features.
* [ ] **Environment Setup:**
    * [ ] Set up local dev environments (Flutter, Flask).
    * [ ] Choose/set up cloud hosting (backend, DB, file storage).
    * [ ] Set up basic CI/CD placeholders.

## Phase 1: Backend Development (Flask API - MVP Core)

* (Tasks as previously defined for PRD Sections 3.2, 3.5, 3.7, 3.6, 3.8, 3.3, 3.4, 3.10 - focusing on Discovery & Basic Community)
* [ ] **Testing & Documentation (MVP APIs)**

## Phase 2: Frontend Development (Flutter App - MVP Core)

* (Tasks as previously defined for PRD Sections 3.1, 3.2, 3.3, 3.4, 3.9, 3.5, 3.7, 3.6, 3.10 - focusing on Discovery & Basic Community)
* [ ] **General Frontend Tasks (MVP)**

## Phase 3: Integration & Testing (MVP)

* (Tasks as previously defined: E2E, Performance, Usability, Device/Network Testing, Security Review, Bug Fixing for MVP features)

## Phase 4: Deployment & Launch (MVP - Discovery & Basic Community)

* (Tasks as previously defined: Infrastructure, Deployment Pipeline, App Store Prep, Build/Submission, Launch Activities, Analytics for MVP)

## Phase 5: Post-MVP Development - E-commerce & Enhanced Features (Ref PRD 4.1 - 4.6)

* [ ] **E-commerce - Phase 1 (Listings & Basic Payments):**
    * [ ] Backend: API for inventory, seller mode, COD flags, transaction logging.
    * [ ] Frontend: UI for seller mode, listing creation, viewing inventory, COD checkout option.
* [ ] **E-commerce - Phase 2 (Secure Escrow Payments):**
    * [ ] Backend: Escrow logic, MoMo payment gateway integration for escrow, dispute flags.
    * [ ] Frontend: UI for escrow payment, tracking escrow status, initiating disputes.
* [ ] **Verification & Trust - Phase 2:**
    * [ ] Backend: API for business claims, owner verification status.
    * [ ] Frontend: UI for claim submission, owner dashboard.
* [ ] **Community - Phase 2:**
    * [ ] Backend: API for creating threads/communities, advanced moderation roles.
    * [ ] Frontend: UI for creating threads, moderation tools (if applicable in-app).
* [ ] **E-commerce - Phase 3 (Advanced Marketplace & RFQ):**
    * [ ] Backend: API for POS-like functions, advanced inventory, promotions, RFQ system (customer requests, vendor bids).
    * [ ] Frontend: UI for these advanced e-commerce features.
* [ ] **Testing & Iteration for each sub-phase.**

## Phase 6: Post-MVP Development - Secure Delivery Platform (Ref PRD 4.7 - Major Feature Set)

This is a substantial phase and will likely be broken into smaller sprints.

### 6.1. Delivery Platform - Backend (Flask API Extensions)
* [ ] **User Role Expansion:** Extend user model and auth to support Sender, Recipient (delivery), Delivery Provider roles.
* [ ] **Secure Address Management API:**
    * [ ] Endpoints for storing encrypted address ciphertext and non-sensitive metadata (Address ID, label). **NO PLAINTEXT ADDRESSES OR KEYS STORED.**
    * [ ] Logic for handling address tokens/IDs.
* [ ] **Order Management API (Delivery):**
    * [ ] CRUD for delivery orders (item details, pickup, encrypted recipient address ID, status).
    * [ ] Logic for assigning Delivery Providers (manual or basic matching initially).
* [ ] **Delivery Provider Management API:**
    * [ ] Endpoints for Delivery Provider registration, verification (document upload handling), profile, availability status.
    * [ ] Endpoints for earnings tracking and payout requests.
* [ ] **Temporary Address Access API:**
    * [ ] **Critical:** Design and implement secure mechanism (PRE or Time-Limited Tokens) for temporary decryption/access for assigned Delivery Provider. Requires robust security and audit logging.
* [ ] **Tracking API:** Endpoints for Delivery Providers to update location/status; endpoints for Senders/Recipients to fetch real-time tracking data.
* [ ] **Payment API (Delivery):**
    * [ ] Integrate MoMo/Card payments for Senders placing delivery orders.
    * [ ] API for processing payouts to Delivery Providers.
* [ ] **Pricing Engine API:** Logic to calculate delivery costs based on defined model (base, distance, time, etc.).
* [ ] **Communication API:** Endpoints for E2EE chat setup between relevant parties for active deliveries.
* [ ] **Rating/Review API (Delivery):** Endpoints for submitting/retrieving ratings for deliveries.
* [ ] **Admin Panel Backend:** APIs for admin oversight of deliveries, providers, disputes.

### 6.2. Delivery Platform - Frontend (Flutter App Extensions)
* [ ] **UI for New User Roles:**
    * [ ] Recipient (Delivery): Manage encrypted addresses (create, view, backup guidance), authorize Senders, track incoming.
    * [ ] Sender (Delivery): Create delivery orders, input pickup/item details, use Recipient address tokens, track outgoing, pay.
    * [ ] Delivery Provider: Onboarding/verification flow, view/accept jobs, navigation UI (map), status updates, proof of delivery, earnings dashboard.
* [ ] **Secure Address Management UI (Recipient):**
    * [ ] **Critical:** Intuitive flow for generating keys, encrypting addresses, understanding backup responsibility. Clear educational components.
    * [ ] UI for sharing address tokens/authorizing Senders.
* [ ] **Temporary Address Access UI (Delivery Provider):**
    * [ ] Secure display of decrypted address within app only during active delivery. Disable copy/paste.
* [ ] **Map Integration (Delivery):** Real-time tracking display, route display for providers.
* [ ] **Communication UI:** In-app E2EE chat interfaces for delivery context.
* [ ] **Payment UI (Delivery):** For Senders to pay, for Providers to see earnings/request payout.
* [ ] **Notifications:** Implement push notifications for all delivery-related events.

### 6.3. Delivery Platform - Specific Security & UX Focus
* [ ] **Key Management UX Design & Testing:** Iterate heavily on making user-managed key backup intuitive and providing clear warnings.
* [ ] **Cryptography Implementation:** Implement client-side encryption (AES-256) and chosen temporary access mechanism securely.
* [ ] **E2EE Chat Implementation.**
* [ ] **Third-Party Security Audit (Recommended for delivery crypto components).**

### 6.4. Delivery Platform - Testing & Iteration
* [ ] **Thorough E2E testing of all delivery flows** (order, encryption, tracking, payment, communication).
* [ ] **Security testing focused on address privacy and key management.**
* [ ] **Usability testing with all three delivery user roles in Accra.**
* [ ] **Performance testing under simulated delivery load.**
* [ ] **Pilot program in a limited area of Accra.**

## Phase 7: Ongoing Maintenance & Future Enhancements (Overall App)

* [ ] **Feedback Collection & Analysis.**
* [ ] **Plan and develop features from PRD Section 9 (SecureDispatch future considerations) or other emerging needs.**
* [ ] **Regular security audits and updates.**
* [ ] **Performance monitoring and optimization.**

