// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'package:wicker/services/config_service.dart'; // Import ConfigService
// import 'package:wicker/widgets/home_search_bar.dart';
// import 'package:wicker/widgets/post_card.dart';
// import 'package:wicker/services/post_service.dart';

// class HomeScreen extends StatefulWidget {
//   final VoidCallback onSearchTap;
//   const HomeScreen({super.key, required this.onSearchTap});

//   @override
//   _HomeScreenState createState() => _HomeScreenState();
// }

// class _HomeScreenState extends State<HomeScreen> {
//   final PostService _postService = PostService();
//   final ConfigService _configService =
//       ConfigService.instance; // Get instance of ConfigService

//   List<Map<String, dynamic>> _posts = [];
//   bool _isLoading = true;
//   String? _error;
//   Timer? _pollingTimer;
//   String _baseUrl = ''; // State variable to hold the base URL

//   @override
//   void initState() {
//     super.initState();
//     _initialize(); // New initialization method

//     // Polling timer remains the same
//     _pollingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
//       print("Polling for new posts...");
//       _fetchPosts();
//     });
//   }

//   @override
//   void dispose() {
//     _pollingTimer?.cancel();
//     super.dispose();
//   }

//   /// Initializes the screen by loading config and fetching posts.
//   Future<void> _initialize() async {
//     await _loadConfig();
//     await _fetchPosts();
//   }

//   /// Loads the base URL from the ConfigService.
//   Future<void> _loadConfig() async {
//     try {
//       final url = await _configService.getBaseUrl();
//       if (mounted) {
//         setState(() {
//           _baseUrl = url;
//         });
//       }
//     } catch (e) {
//       if (mounted) {
//         setState(() {
//           _error = "Failed to load server configuration.";
//         });
//       }
//     }
//   }

//   /// Fetches the list of posts from the server.
//   Future<void> _fetchPosts() async {
//     // Ensure config is loaded before fetching
//     if (_baseUrl.isEmpty) {
//       await _loadConfig();
//     }

//     setState(() => _isLoading = true);
//     try {
//       final posts = await _postService.getPosts();
//       if (mounted) {
//         setState(() {
//           _posts = posts;
//           _isLoading = false;
//           _error = null;
//         });
//       }
//     } catch (e) {
//       if (mounted) {
//         setState(() {
//           _error = e.toString();
//           _isLoading = false;
//         });
//       }
//     }
//   }

//   /// Processes the raw post data to prepare it for the PostCard widget.
//   List<Map<String, dynamic>> _processFetchedPosts(
//     List<Map<String, dynamic>> posts,
//   ) {
//     // This function now uses the _baseUrl loaded from ConfigService
//     // It no longer needs to create its own.
//     for (var post in posts) {
//       final authorDetails = post['author_details'] as Map<String, dynamic>?;
//       // The avatar URL logic from PostCard can be centralized here if desired,
//       // but for now, we'll let PostCard handle it to ensure consistency.
//       post['avatarUrl'] =
//           authorDetails?['profile_pic_url']?.toString() ??
//           'https://i.pravatar.cc/150?u=${authorDetails?['_id']?['\$oid']}';
//     }
//     return posts;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: Column(
//         children: [
//           Container(
//             padding: const EdgeInsets.only(top: 40.0, bottom: 8.0),
//             child: HomeSearchBar(onTap: widget.onSearchTap),
//           ),
//           Expanded(child: _buildBodyContent()),
//         ],
//       ),
//     );
//   }

//   Widget _buildBodyContent() {
//     if (_isLoading) {
//       return const Center(child: CircularProgressIndicator());
//     }
//     if (_error != null) {
//       return Center(child: Text('Error: $_error'));
//     }
//     if (_posts.isEmpty) {
//       return const Center(child: Text('No posts found. Create one!'));
//     }

//     // Process the posts before displaying them
//     final allPosts = _processFetchedPosts(_posts);

//     return RefreshIndicator(
//       onRefresh: _fetchPosts,
//       child: ListView.builder(
//         padding: EdgeInsets.zero,
//         itemCount: allPosts.length,
//         itemBuilder: (context, index) {
//           final post = allPosts[index];
//           // Pass the baseUrl to the PostCard
//           return PostCard(postData: post);
//         },
//       ),
//     );
//   }
// }

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wicker/widgets/home_search_bar.dart';
import 'package:wicker/widgets/post_card.dart';
import 'package:wicker/services/post_service.dart';

class HomeScreen extends StatefulWidget {
  final VoidCallback onSearchTap;
  const HomeScreen({super.key, required this.onSearchTap});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PostService _postService = PostService();
  late Future<List<Map<String, dynamic>>> _postsFuture;

  @override
  void initState() {
    super.initState();
    _postsFuture = _postService.getPosts();
    // Polling can be added back if desired
  }

  Future<void> _refreshPosts() async {
    setState(() {
      _postsFuture = _postService.getPosts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.only(top: 40.0, bottom: 8.0),
            child: HomeSearchBar(onTap: widget.onSearchTap),
          ),
          Expanded(child: _buildBodyContent()),
        ],
      ),
    );
  }

  Widget _buildBodyContent() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _postsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('No posts found. Create one!'));
        }

        final allPosts = snapshot.data!;

        return RefreshIndicator(
          onRefresh: _refreshPosts,
          child: ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: allPosts.length,
            itemBuilder: (context, index) {
              final post = allPosts[index];
              // Simply pass the raw data. The PostCard handles the rest.
              return PostCard(postData: post);
            },
          ),
        );
      },
    );
  }
}
