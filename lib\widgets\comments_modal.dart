import 'dart:async';
import 'package:flutter/material.dart';
import 'package:wicker/services/comment_service.dart';
import 'package:wicker/widgets/comment_tile.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class CommentsModal extends StatefulWidget {
  final String postId;
  const CommentsModal({super.key, required this.postId});

  @override
  _CommentsModalState createState() => _CommentsModalState();
}

class _CommentsModalState extends State<CommentsModal> {
  final CommentService _commentService = CommentService();
  final TextEditingController _commentController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;

  String? _replyingToCommentId;
  String? _replyingToUsername;
  late Future<List<Map<String, dynamic>>> _commentsFuture;
  String _activeSentimentFilter = 'all';
  bool _didPostComment = false;

  @override
  void initState() {
    super.initState();
    // --- THE FIX: Call getComments with the correct parameters ---
    _commentsFuture = _commentService.getComments(
      parentId: widget.postId,
      parentType: 'post', // Specify that we are fetching comments for a post
    );
    // --- End of FIX ---
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _commentController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), _refreshComments);
  }

  Future<void> _refreshComments() async {
    setState(() {
      _commentsFuture = _commentService.getComments(
        parentId: widget.postId,
        parentType: 'post', // Also specify the parentType here
        searchQuery: _searchController.text,
        sentiment: _activeSentimentFilter,
      );
    });
  }

  // In _CommentsModalState

  void _postComment() async {
    if (_commentController.text.trim().isEmpty) return;

    try {
      // --- THE FIX: Pass the parentId when replying ---
      await _commentService.postComment(
        postId: widget.postId,
        commentText: _commentController.text,
        parentId:
            _replyingToCommentId, // Pass the ID of the comment being replied to
      );
      // --- End of FIX ---

      _didPostComment = true;
      _commentController.clear();
      _cancelReply(); // Clear the "replying to" state
      _refreshComments(); // Refresh the list to show the new reply
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to post comment: $e')));
      }
    }
  }

  void _startReply(String commentId, String username) {
    // This logic for threaded replies will need to be adjusted
    // when you fully implement them, but it is correct for now.
    setState(() {
      _replyingToCommentId = commentId;
      _replyingToUsername = username;
    });
  }

  void _cancelReply() {
    setState(() {
      _replyingToCommentId = null;
      _replyingToUsername = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.pop(context, _didPostComment);
        return true;
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFFEF7F0),
        body: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildFilterChips(),
              Expanded(child: _buildCommentsList()),
              _buildCommentInputField(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Comments",
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          NeuCard(
            margin: EdgeInsets.zero,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search comments...',
                border: InputBorder.none,
                icon: Icon(EvaIcons.search),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    final sentiments = ['all', 'positive', 'negative', 'neutral'];
    final colors = [
      const Color(0xFF6C5CE7), // Blue
      const Color(0xFF4ECDC4), // Teal
      const Color(0xFFFF6B6B), // Coral
      Colors.grey.shade400,
    ];

    return SizedBox(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: sentiments.length,
        itemBuilder: (context, index) {
          final sentiment = sentiments[index];
          final bool isSelected = _activeSentimentFilter == sentiment;
          return GestureDetector(
            onTap: () {
              setState(() => _activeSentimentFilter = sentiment);
              _refreshComments();
            },
            child: NeuCard(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              backgroundColor: isSelected ? colors[index] : Colors.white,
              shadowOffset: 4,
              borderWidth: 2,
              child: Text(
                sentiment.toUpperCase(),
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCommentsList() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _commentsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('No comments yet. Be the first!'));
        }
        final comments = _buildCommentTree(snapshot.data!);
        return RefreshIndicator(
          onRefresh: _refreshComments,
          child: ListView(
            padding: const EdgeInsets.only(bottom: 16),
            children: comments,
          ),
        );
      },
    );
  }

  List<Widget> _buildCommentTree(
    List<Map<String, dynamic>> comments, {
    String? parentId,
    int depth = 0,
  }) {
    List<Widget> tree = [];
    final children = comments
        .where((c) => c['parent_id']?['\$oid'] == parentId)
        .toList();

    for (var comment in children) {
      tree.add(
        CommentTile(commentData: comment, depth: depth, onReply: _startReply),
      );
      tree.addAll(
        _buildCommentTree(
          comments,
          parentId: comment['_id']['\$oid'],
          depth: depth + 1,
        ),
      );
    }
    return tree;
  }

  Widget _buildCommentInputField() {
    return NeuCard(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.fromLTRB(16, 8, 8, 8),
      backgroundColor: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_replyingToUsername != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Row(
                children: [
                  Text(
                    "Replying to $_replyingToUsername",
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: _cancelReply,
                    child: const Icon(Icons.close, size: 16),
                  ),
                ],
              ),
            ),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _commentController,
                  decoration: const InputDecoration(
                    hintText: "Add a comment...",
                    border: InputBorder.none,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: _postComment,
                child: const NeuCard(
                  margin: EdgeInsets.zero,
                  padding: EdgeInsets.all(12),
                  backgroundColor: Color(0xFF00D2D3), // Cyan
                  child: Icon(EvaIcons.paperPlane, color: Colors.white),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
