# Wicker IP Address Update Scripts

This directory contains scripts to automatically update IP addresses throughout the Flutter project when your development server changes networks.

## Available Scripts

### 1. <PERSON><PERSON> (Cross-platform) - `update_ip.dart`
**Recommended for most users**

```bash
# Auto-discover server IP
dart scripts/update_ip.dart

# Update to specific IP
dart scripts/update_ip.dart *************
```

**Features:**
- ✅ Cross-platform (Windows, macOS, Linux)
- ✅ Auto-discovery with network scanning
- ✅ Socket-based connectivity testing
- ✅ Comprehensive file pattern matching
- ✅ Automatic documentation updates

### 2. PowerShell Script (Windows) - `update_ip.ps1`

```powershell
# Auto-discover server IP
.\scripts\update_ip.ps1

# Update to specific IP
.\scripts\update_ip.ps1 *************
```

**Features:**
- ✅ Native Windows PowerShell
- ✅ Auto-discovery with network scanning
- ✅ TCP connectivity testing
- ✅ Windows-optimized file operations

### 3. Bash Script (Unix/Linux/macOS) - `update_ip.sh`

```bash
# Make executable (first time only)
chmod +x scripts/update_ip.sh

# Auto-discover server IP
./scripts/update_ip.sh

# Update to specific IP
./scripts/update_ip.sh *************
```

**Features:**
- ✅ Native Unix/Linux/macOS support
- ✅ Auto-discovery with network scanning
- ✅ Netcat-based connectivity testing
- ✅ Sed-based file operations

## How Auto-Discovery Works

All scripts use the following strategy to find your server:

1. **Detect current device network** - Determines your device's IP to identify the network range
2. **Scan common network ranges**:
   - Your current network (e.g., 192.168.8.x)
   - 192.168.1.x (common home networks)
   - 192.168.0.x (common router default)
   - 10.0.0.x (corporate networks)
   - 172.16.0.x (private networks)
3. **Test common server IPs** in each range:
   - .1 (router/gateway)
   - .100, .101 (common static IPs)
   - .107, .219 (previously used IPs)
4. **Verify connectivity** by attempting to connect to port 5000

## Files Updated

The scripts automatically update IP addresses in these files:

- `lib/services/auth_service.dart`
- `lib/services/post_service.dart`
- `lib/services/places_service.dart`
- `lib/services/queue_service.dart`
- `lib/services/playlist_service.dart`
- `lib/screens/home_screen.dart`
- `lib/screens/explore_screen.dart`
- `lib/widgets/post_card.dart`
- `lib/widgets/place_detail_card.dart`
- `lib/widgets/user_contributions_tab.dart`
- `IP_ADDRESS_UPDATE_SUMMARY.md` (documentation)

## Platform Strategy

The scripts maintain Flutter's cross-platform compatibility by using this pattern:

```dart
final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
    ? "http://YOUR_IP:5000"      // Physical Android device
    : "http://127.0.0.1:5000";   // Web/iOS/Desktop
```

This ensures:
- **Android physical devices** connect to your development server
- **Web, iOS, and desktop** apps connect to localhost
- **Android emulators** can use either depending on configuration

## Troubleshooting

### Auto-discovery fails
```bash
# Try manual IP specification
dart scripts/update_ip.dart *************

# Check if server is running
curl http://*************:5000/api/health
# or
telnet ************* 5000
```

### Permission errors (Unix/Linux/macOS)
```bash
# Make script executable
chmod +x scripts/update_ip.sh

# Run with explicit bash
bash scripts/update_ip.sh
```

### PowerShell execution policy (Windows)
```powershell
# Allow script execution (run as Administrator)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Or run with bypass
powershell -ExecutionPolicy Bypass -File scripts/update_ip.ps1
```

### Network connectivity issues
1. Ensure both devices are on the same network
2. Check firewall settings on the server machine
3. Verify the server is actually running on port 5000
4. Try pinging the IP address first: `ping *************`

## Manual Configuration

If scripts don't work, you can also use the in-app settings:

1. Open the Wicker app
2. Go to the Hub screen (profile tab)
3. Tap the settings icon (⚙️)
4. Use "Auto-Discover Server" or manually enter IP
5. Save configuration

## After Running Scripts

1. **Install dependencies**: `flutter pub get`
2. **Restart your app** to pick up the new configuration
3. **Test connectivity** by trying to log in or load data
4. **Check server logs** to verify requests are reaching your server

## Development Workflow

For frequent IP changes during development:

```bash
# Quick update when switching networks
dart scripts/update_ip.dart

# Or create an alias for convenience
alias update-wicker-ip="dart scripts/update_ip.dart"
```

The scripts are designed to be safe and can be run multiple times without issues.
