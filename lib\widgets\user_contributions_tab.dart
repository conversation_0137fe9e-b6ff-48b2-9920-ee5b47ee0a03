import 'package:flutter/material.dart';
import 'package:wicker/services/user_service.dart';
import 'package:wicker/widgets/post_card.dart';

class UserContributionsTab extends StatefulWidget {
  const UserContributionsTab({super.key});

  @override
  State<UserContributionsTab> createState() => _UserContributionsTabState();
}

class _UserContributionsTabState extends State<UserContributionsTab> {
  final UserService _userService = UserService();
  late Future<List<Map<String, dynamic>>> _myContributionsFuture;

  @override
  void initState() {
    super.initState();
    _myContributionsFuture = _userService.getMyContributions();
  }

  Future<void> _refreshContributions() async {
    setState(() {
      _myContributionsFuture = _userService.getMyContributions();
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _myContributionsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Text("You haven't made any contributions yet."),
          );
        }

        final contributions = snapshot.data!;

        return RefreshIndicator(
          onRefresh: _refreshContributions,
          child: ListView.builder(
            padding: const EdgeInsets.only(top: 8, bottom: 8),
            itemCount: contributions.length,
            itemBuilder: (context, index) {
              final item = contributions[index];
              // THE FIX: Simply pass the raw data directly to the PostCard.
              // The PostCard now handles all the necessary processing itself.
              return PostCard(postData: item);
            },
          ),
        );
      },
    );
  }
}
