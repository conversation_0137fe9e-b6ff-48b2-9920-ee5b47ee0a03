import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:wicker/widgets/aspect_ratio_container.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class MediaPlayer extends StatefulWidget {
  final Map<String, dynamic> mediaData;
  final String baseUrl;

  const MediaPlayer({
    super.key,
    required this.mediaData,
    required this.baseUrl,
  });

  @override
  State<MediaPlayer> createState() => _MediaPlayerState();
}

class _MediaPlayerState extends State<MediaPlayer> {
  // We now use a Future to hold the initialization state
  Future<VideoPlayerController>? _videoControllerFuture;

  @override
  void initState() {
    super.initState();
    if (widget.mediaData['type'] == 'video') {
      final videoPath = widget.mediaData['path']?.replaceAll('\\', '/') ?? '';
      final videoUrl = '${widget.baseUrl}/$videoPath';
      // Create the controller but don't wait for it here
      final controller = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
      // Store the initialization Future in our state variable
      _videoControllerFuture = controller.initialize().then((_) => controller);
    }
  }

  @override
  Widget build(BuildContext context) {
    final String type = widget.mediaData['type'] ?? 'unknown';

    switch (type) {
      case 'image':
        final path = widget.mediaData['path']?.replaceAll('\\', '/') ?? '';
        final mediaUrl = '${widget.baseUrl}/$path';
        final aspectRatio = (widget.mediaData['aspect_ratio'] as num?)?.toDouble() ?? 16/9;
        return AspectRatioContainer(imageUrl: mediaUrl, aspectRatio: aspectRatio);

      case 'video':
        // THE FIX: Use a FutureBuilder to handle the video initialization
        return FutureBuilder<VideoPlayerController>(
          future: _videoControllerFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
              final controller = snapshot.data!;
              return AspectRatio(
                aspectRatio: controller.value.aspectRatio,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    VideoPlayer(controller),
                    IconButton(
                      icon: Icon(
                        controller.value.isPlaying ? Icons.pause_circle_filled : Icons.play_circle_filled,
                        color: Colors.white,
                        size: 60,
                      ),
                      onPressed: () => setState(() {
                        controller.value.isPlaying ? controller.pause() : controller.play();
                      }),
                    )
                  ],
                ),
              );
            } else {
              // While initializing, show a loading spinner
              return Container(color: Colors.black, child: const Center(child: CircularProgressIndicator()));
            }
          },
        );
        
      case 'audio':
        return Container(height: 300, color: Colors.grey.shade200, child: const Center(child: Icon(EvaIcons.music, size: 80)));
        
      default:
        return Container(height: 300, color: Colors.grey, child: const Center(child: Text('Unsupported Media')));
    }
  }
}