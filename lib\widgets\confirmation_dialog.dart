import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';

class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final VoidCallback onConfirm;

  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Text(content),
      actions: <Widget>[
        NeuTextButton(
          enableAnimation: true,
          onPressed: () => Navigator.of(context).pop(), // Dismiss the dialog
          text: const Text("Cancel"),
        ),
        NeuTextButton(
          enableAnimation: true,
          onPressed: () {
            Navigator.of(context).pop(); // Dismiss the dialog
            onConfirm(); // Execute the confirmation action
          },
          buttonColor: Colors.red.shade700,
          text: const Text("Delete", style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }
}