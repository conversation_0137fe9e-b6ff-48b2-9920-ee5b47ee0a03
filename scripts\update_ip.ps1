# PowerShell script to update IP addresses in the Flutter project
# Usage: .\scripts\update_ip.ps1 [ip_address]

param(
    [string]$IpAddress = ""
)

Write-Host "🔧 Wicker IP Address Updater (PowerShell)" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Function to test if an IP address is valid
function Test-IpAddress {
    param([string]$ip)
    
    try {
        $octets = $ip.Split('.')
        if ($octets.Length -ne 4) { return $false }
        
        foreach ($octet in $octets) {
            $num = [int]$octet
            if ($num -lt 0 -or $num -gt 255) { return $false }
        }
        return $true
    }
    catch {
        return $false
    }
}

# Function to test server connectivity
function Test-ServerConnection {
    param(
        [string]$ip,
        [int]$port = 5000,
        [int]$timeoutMs = 2000
    )
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $asyncResult = $tcpClient.BeginConnect($ip, $port, $null, $null)
        $wait = $asyncResult.AsyncWaitHandle.WaitOne($timeoutMs, $false)
        
        if ($wait) {
            $tcpClient.EndConnect($asyncResult)
            $tcpClient.Close()
            return $true
        }
        else {
            $tcpClient.Close()
            return $false
        }
    }
    catch {
        return $false
    }
}

# Function to auto-discover server IP
function Find-ServerIp {
    Write-Host "🔍 Auto-discovering server IP address..." -ForegroundColor Yellow
    
    # Get current device IP to determine network range
    $deviceIp = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -notlike "127.*" -and $_.IPAddress -notlike "169.254.*" } | Select-Object -First 1).IPAddress
    
    $networkRanges = @()
    
    if ($deviceIp) {
        $parts = $deviceIp.Split('.')
        if ($parts.Length -ge 3) {
            $networkRanges += "$($parts[0]).$($parts[1]).$($parts[2])"
        }
    }
    
    # Add common network ranges
    $networkRanges += @("192.168.1", "192.168.0", "192.168.8", "10.0.0", "172.16.0")
    
    foreach ($range in $networkRanges) {
        $commonIps = @("$range.1", "$range.100", "$range.101", "$range.107", "$range.219")
        
        foreach ($ip in $commonIps) {
            Write-Host "   Testing $ip..." -ForegroundColor Gray
            if (Test-ServerConnection -ip $ip) {
                Write-Host "✅ Found server at: $ip" -ForegroundColor Green
                return $ip
            }
        }
    }
    
    return $null
}

# Main logic
if (-not $IpAddress) {
    $IpAddress = Find-ServerIp
    
    if (-not $IpAddress) {
        Write-Host "❌ Could not auto-discover server IP." -ForegroundColor Red
        Write-Host "💡 Usage: .\scripts\update_ip.ps1 <ip_address>" -ForegroundColor Yellow
        Write-Host "   Example: .\scripts\update_ip.ps1 *************" -ForegroundColor Yellow
        exit 1
    }
}
else {
    Write-Host "📝 Using provided IP: $IpAddress" -ForegroundColor Green
}

# Validate IP format
if (-not (Test-IpAddress -ip $IpAddress)) {
    Write-Host "❌ Invalid IP address format: $IpAddress" -ForegroundColor Red
    exit 1
}

Write-Host "🔄 Updating IP addresses in project files..." -ForegroundColor Yellow

# Files to update
$filesToUpdate = @(
    "lib\services\auth_service.dart",
    "lib\services\post_service.dart",
    "lib\services\places_service.dart",
    "lib\services\queue_service.dart",
    "lib\services\playlist_service.dart",
    "lib\screens\home_screen.dart",
    "lib\screens\explore_screen.dart",
    "lib\widgets\post_card.dart",
    "lib\widgets\place_detail_card.dart",
    "lib\widgets\user_contributions_tab.dart"
)

$updatedFiles = 0

foreach ($filePath in $filesToUpdate) {
    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        $originalContent = $content
        
        # Replace IP patterns (excluding localhost)
        $content = $content -replace '"http://\d+\.\d+\.\d+\.\d+:5000"', "`"http://$IpAddress:5000`""
        $content = $content -replace '"\d+\.\d+\.\d+\.\d+"', "`"$IpAddress`""
        
        # Restore localhost if it was accidentally replaced
        $content = $content -replace "`"http://$IpAddress:5000`"", "`"http://127.0.0.1:5000`""
        $content = $content -replace "`"$IpAddress`"", "`"127.0.0.1`""
        
        # Re-apply Android IP replacement
        $content = $content -replace 'defaultTargetPlatform == TargetPlatform\.android\s*\?\s*"http://127\.0\.0\.1:5000"', "defaultTargetPlatform == TargetPlatform.android ? `"http://$IpAddress:5000`""
        $content = $content -replace 'defaultTargetPlatform == TargetPlatform\.android\s*\?\s*"127\.0\.0\.1"', "defaultTargetPlatform == TargetPlatform.android ? `"$IpAddress`""
        
        if ($content -ne $originalContent) {
            Set-Content $filePath -Value $content -NoNewline
            Write-Host "✅ Updated: $filePath" -ForegroundColor Green
            $updatedFiles++
        }
        else {
            Write-Host "ℹ️  No changes needed: $filePath" -ForegroundColor Gray
        }
    }
    else {
        Write-Host "⚠️  File not found: $filePath" -ForegroundColor Yellow
    }
}

# Update summary document
$timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss"
$summaryContent = @"
# IP Address Update Summary

## Latest Update: $timestamp

**Current IP Address: $IpAddress**

## Overview

Updated all IP addresses throughout the Flutter application to use ``http://$IpAddress:5000`` for Android physical device testing, while maintaining ``http://127.0.0.1:5000`` for web and iOS platforms.

## Auto-Update Scripts

This project includes automatic IP update scripts:

``````bash
# Dart script (cross-platform)
dart scripts/update_ip.dart

# PowerShell script (Windows)
.\scripts\update_ip.ps1

# Update to specific IP
dart scripts/update_ip.dart *************
.\scripts\update_ip.ps1 *************
``````

## Platform Detection Strategy

All services use the following pattern for cross-platform compatibility:

``````dart
final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
    ? "http://$IpAddress:5000"  // Physical Android device
    : "http://127.0.0.1:5000";      // Web/iOS/Desktop
``````

## Network Configuration

The app is configured to work with:
- **Android Physical Device**: ``http://$IpAddress:5000``
- **Web/iOS/Desktop**: ``http://127.0.0.1:5000``
- **Android Manifest**: Already configured with ``android:usesCleartextTraffic="true"`` for HTTP traffic

## Next Steps for Testing

1. Ensure the backend server is running on ``$IpAddress:5000``
2. Connect Android device to the same network
3. Build and install the app on the physical device:
   ``````bash
   flutter build apk --debug
   flutter install
   ``````
4. Test all network-dependent features (authentication, posts, places, etc.)

---
*This document was automatically updated by the IP update script.*
"@

Set-Content "IP_ADDRESS_UPDATE_SUMMARY.md" -Value $summaryContent
Write-Host "✅ Updated: IP_ADDRESS_UPDATE_SUMMARY.md" -ForegroundColor Green

Write-Host ""
Write-Host "📊 Summary:" -ForegroundColor Cyan
Write-Host "   Files updated: $updatedFiles" -ForegroundColor White
Write-Host "   New IP address: $IpAddress" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Run: flutter pub get" -ForegroundColor White
Write-Host "   2. Restart your app" -ForegroundColor White
Write-Host "   3. Test connectivity to http://$IpAddress:5000" -ForegroundColor White
