import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:wicker/services/places_service.dart';
import 'auth_service.dart';

class PostService {
  final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
      ? "http://192.168.8.107:5000"
      : "http://127.0.0.1:5000";

  final AuthService _authService = AuthService();

  final WickerHttpClient _client = WickerHttpClient();

  // THE FIX: Renamed to getPosts and uses the custom client
  Future<List<Map<String, dynamic>>> getPosts() async {
    try {
      final response = await _client.get(Uri.parse('$_baseUrl/api/posts/'));

      if (response.statusCode == 200) {
        List<dynamic> posts = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(posts);
      } else {
        throw Exception(
          'Failed to load posts. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Get posts error: $e');
      rethrow;
    }
  }

  // In lib/services/post_service.dart -> class PostService

  Future<List<Map<String, dynamic>>> getPostsByUser(String userId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/api/posts/user/$userId'),
      );
      if (response.statusCode == 200) {
        List<dynamic> posts = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(posts);
      } else {
        throw Exception('Failed to load user posts');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getPostsForBusiness(
    String businessId,
  ) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/api/posts/tagged/business/$businessId'),
      );
      if (response.statusCode == 200) {
        List<dynamic> posts = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(posts);
      } else {
        throw Exception('Failed to load business posts');
      }
    } catch (e) {
      rethrow;
    }
  }

  // NEW: Method to fetch the user's posts (different from getPosts which gets all post from all users)

  Future<List<Map<String, dynamic>>> getMyPosts() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/api/posts/my-posts'),
      );
      if (response.statusCode == 200) {
        List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load user posts');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> likePost(String postId) async {
    try {
      await _client.post(Uri.parse('$_baseUrl/api/posts/$postId/like'));
    } catch (e) {
      rethrow;
    }
  }

  Future<void> dislikePost(String postId) async {
    try {
      await _client.post(Uri.parse('$_baseUrl/api/posts/$postId/dislike'));
    } catch (e) {
      rethrow;
    }
  }

  Future<String> createPost({
    required String textContent,
    String? taggedPlaceId,
    List<XFile>? mediaFiles,
  }) async {
    final token = await _authService.getAccessToken();
    if (token == null) throw Exception('Authentication Token not found.');

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$_baseUrl/api/posts/create'),
    );
    request.headers['Authorization'] = 'Bearer $token';

    request.fields['text_content'] = textContent;
    if (taggedPlaceId != null) {
      request.fields['tagged_place_id'] = taggedPlaceId;
    }

    // Handle media file uploads with proper MIME types
    if (mediaFiles != null && mediaFiles.isNotEmpty) {
      for (var mediaFile in mediaFiles) {
        // Determine the correct MIME type
        MediaType contentType = _getMediaType(mediaFile);

        if (kIsWeb) {
          // For web, read the file as bytes
          var bytes = await mediaFile.readAsBytes();
          request.files.add(
            http.MultipartFile.fromBytes(
              'media',
              bytes,
              filename: mediaFile.name,
              contentType: contentType,
            ),
          );
        } else {
          // For mobile, read from the path
          request.files.add(
            await http.MultipartFile.fromPath(
              'media',
              mediaFile.path,
              contentType: contentType,
            ),
          );
        }
      }
    }

    try {
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);
      final responseBody = jsonDecode(response.body);

      if (response.statusCode == 201) {
        return responseBody['msg'] ?? 'Post created successfully';
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to create post');
      }
    } catch (e) {
      print('Create post error: $e');
      rethrow;
    }
  }

  /// Helper method to determine the correct MIME type for media files
  MediaType _getMediaType(XFile mediaFile) {
    // First try to get MIME type from the file
    String? mimeType = mediaFile.mimeType;

    // If not available, detect from file extension
    if (mimeType == null || mimeType.isEmpty) {
      mimeType = lookupMimeType(mediaFile.path);
    }

    // Parse the MIME type
    if (mimeType != null) {
      final parts = mimeType.split('/');
      if (parts.length == 2) {
        return MediaType(parts[0], parts[1]);
      }
    }

    // Fallback based on file extension
    String path = mediaFile.path.toLowerCase();
    if (path.endsWith('.mp4')) {
      return MediaType('video', 'mp4');
    } else if (path.endsWith('.mov')) {
      return MediaType('video', 'quicktime');
    } else if (path.endsWith('.avi')) {
      return MediaType('video', 'x-msvideo');
    } else if (path.endsWith('.webm')) {
      return MediaType('video', 'webm');
    } else if (path.endsWith('.3gp')) {
      return MediaType('video', '3gpp');
    } else if (path.endsWith('.jpg') || path.endsWith('.jpeg')) {
      return MediaType('image', 'jpeg');
    } else if (path.endsWith('.png')) {
      return MediaType('image', 'png');
    } else if (path.endsWith('.gif')) {
      return MediaType('image', 'gif');
    } else if (path.endsWith('.webp')) {
      return MediaType('image', 'webp');
    }

    // Final fallback
    return MediaType('application', 'octet-stream');
  }
}
