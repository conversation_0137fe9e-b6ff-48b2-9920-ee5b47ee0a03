# PowerShell script to test server connectivity
param(
    [string]$IpAddress = "*************",
    [int]$Port = 5000
)

Write-Host "🔧 Wicker Server Connectivity Test" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host "Testing server at: ${IpAddress}:${Port}" -ForegroundColor Yellow
Write-Host ""

# Test 1: Ping test
Write-Host "1. Testing ping connectivity..." -ForegroundColor Blue
try {
    $pingResult = Test-Connection -ComputerName $IpAddress -Count 2 -Quiet
    if ($pingResult) {
        Write-Host "   ✅ Ping successful" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Ping failed" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Ping error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: Port connectivity
Write-Host "2. Testing port connectivity..." -ForegroundColor Blue
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $asyncResult = $tcpClient.BeginConnect($IpAddress, $Port, $null, $null)
    $wait = $asyncResult.AsyncWaitHandle.WaitOne(5000, $false)
    
    if ($wait) {
        $tcpClient.EndConnect($asyncResult)
        Write-Host "   ✅ Port $Port is open" -ForegroundColor Green
        $tcpClient.Close()
    } else {
        Write-Host "   ❌ Port $Port is closed or filtered" -ForegroundColor Red
        $tcpClient.Close()
    }
} catch {
    Write-Host "   ❌ Port test error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: HTTP connectivity
Write-Host "3. Testing HTTP connectivity..." -ForegroundColor Blue
try {
    $url = "http://${IpAddress}:${Port}"
    $response = Invoke-WebRequest -Uri $url -TimeoutSec 10 -UseBasicParsing
    Write-Host "   ✅ HTTP connection successful" -ForegroundColor Green
    Write-Host "   📊 Status Code: $($response.StatusCode)" -ForegroundColor Gray
    Write-Host "   📏 Response Length: $($response.Content.Length) bytes" -ForegroundColor Gray
} catch {
    Write-Host "   ❌ HTTP connection failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 4: API endpoints
Write-Host "4. Testing API endpoints..." -ForegroundColor Blue
$endpoints = @("/api/health", "/api/auth/login", "/api/auth/register")

foreach ($endpoint in $endpoints) {
    try {
        $url = "http://${IpAddress}:${Port}$endpoint"
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -UseBasicParsing
        Write-Host "   ✅ $endpoint : $($response.StatusCode)" -ForegroundColor Green
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode) {
            Write-Host "   ⚠️  $endpoint : $statusCode (server responded)" -ForegroundColor Yellow
        } else {
            Write-Host "   ❌ $endpoint : Connection failed" -ForegroundColor Red
        }
    }
}

Write-Host ""

# Test 5: Login API test
Write-Host "5. Testing login API..." -ForegroundColor Blue
try {
    $loginUrl = "http://${IpAddress}:${Port}/api/auth/login"
    $body = @{
        email = "<EMAIL>"
        password = "testpass"
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri $loginUrl -Method POST -Body $body -ContentType "application/json" -TimeoutSec 10 -UseBasicParsing
    Write-Host "   ✅ Login API responded: $($response.StatusCode)" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    if ($statusCode) {
        Write-Host "   ⚠️  Login API responded: $statusCode (expected for invalid credentials)" -ForegroundColor Yellow
    } else {
        Write-Host "   ❌ Login API connection failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""

# Network information
Write-Host "6. Network Information:" -ForegroundColor Blue
Write-Host "   Computer IP addresses:" -ForegroundColor Gray
Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -notlike "127.*" -and $_.IPAddress -notlike "169.254.*" } | ForEach-Object {
    Write-Host "     - $($_.IPAddress)" -ForegroundColor Gray
}

Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "   • If ping fails: Check if devices are on same network" -ForegroundColor White
Write-Host "   • If port is closed: Check if server is running" -ForegroundColor White
Write-Host "   • If HTTP fails: Check firewall settings" -ForegroundColor White
Write-Host "   • If API fails: Check server configuration" -ForegroundColor White
Write-Host ""
Write-Host "💡 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Ensure your server is running on ${IpAddress}:${Port}" -ForegroundColor White
Write-Host "   2. Check Windows Firewall settings" -ForegroundColor White
Write-Host "   3. Verify both devices are on the same network" -ForegroundColor White
Write-Host "   4. Try running the app's Network Diagnostics" -ForegroundColor White
