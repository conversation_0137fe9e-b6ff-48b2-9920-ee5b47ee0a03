// import 'package:flutter/material.dart';
// import 'package:wicker/services/post_service.dart';
// import 'package:wicker/widgets/add_to_playlist_modal.dart';
// import 'package:wicker/widgets/comments_modal.dart';
// import 'image_post_view.dart';
// import 'video_post_view.dart';
// import 'voice_note_view.dart';
// import 'package:eva_icons_flutter/eva_icons_flutter.dart';
// import 'package:wicker/services/share_service.dart';

// class CategoryFeedView extends StatefulWidget {
//   final int initialIndex;
//   final List<Map<String, dynamic>> items;

//   const CategoryFeedView({
//     super.key,
//     required this.items,
//     required this.initialIndex,
//   });

//   @override
//   _CategoryFeedViewState createState() => _CategoryFeedViewState();
// }

// class _CategoryFeedViewState extends State<CategoryFeedView> {
//   late PageController _pageController;
//   late int _currentIndex;
//   final PostService _postService = PostService();
//   final ShareService _shareService = ShareService();

//   // State for the currently viewed item
//   late bool _isLiked;
//   late int _likeCount;
//   late int _commentCount; // New state for comment count

//   @override
//   void initState() {
//     super.initState();
//     _currentIndex = widget.initialIndex;
//     _pageController = PageController(initialPage: _currentIndex);
//     _updateStateForCurrentItem();
//   }

//   @override
//   void dispose() {
//     _pageController.dispose();
//     super.dispose();
//   }

//   void _updateStateForCurrentItem() {
//     if (!mounted) return;
//     final currentItem = widget.items[_currentIndex];
//     setState(() {
//       _isLiked = currentItem['isLiked'] as bool? ?? false;
//       _likeCount = (currentItem['likes'] as List?)?.length ?? 0;
//       // --- THE FIX: Update comment count from item data ---
//       _commentCount = (currentItem['comments'] as List?)?.length ?? 0;
//       // --- End of FIX ---
//     });
//   }

//   void _onLike() {
//     final currentItem = widget.items[_currentIndex];
//     final postId = currentItem['_id']?['\$oid'] as String?;
//     if (postId == null) return;

//     final originalIsLiked = _isLiked;
//     final originalLikeCount = _likeCount;

//     setState(() {
//       _isLiked = !_isLiked;
//       _likeCount += _isLiked ? 1 : -1;
//     });

//     _postService.likePost(postId).catchError((e) {
//       if (mounted) {
//         setState(() {
//           _isLiked = originalIsLiked;
//           _likeCount = originalLikeCount;
//         });
//         ScaffoldMessenger.of(
//           context,
//         ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
//       }
//     });
//   }

//   void _onComment() {
//     final postId = widget.items[_currentIndex]['_id']?['\$oid'] as String?;
//     if (postId == null) return;
//     showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       builder: (context) => FractionallySizedBox(
//         heightFactor: 0.9,
//         child: CommentsModal(postId: postId),
//       ),
//     );
//   }

//   void _onSave() {
//     final currentItem = widget.items[_currentIndex];
//     final itemId = currentItem['_id']?['\$oid'] as String?;
//     if (itemId == null) return;
//     final itemType = currentItem['content_type'] as String? ?? 'post';
//     showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       builder: (context) =>
//           AddToPlaylistModal(itemId: itemId, itemType: itemType),
//     );
//   }

//   void _onShare() {
//     final currentItem = widget.items[_currentIndex];
//     final String shareText =
//         'Check out this from Wicker: ${currentItem['title']}';
//     _shareService.shareContent(shareText);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Stack(
//       children: [
//         PageView.builder(
//           controller: _pageController,
//           scrollDirection: Axis.vertical,
//           onPageChanged: (index) {
//             _currentIndex = index;
//             _updateStateForCurrentItem();
//           },
//           itemCount: widget.items.length,
//           itemBuilder: (context, index) {
//             final item = widget.items[index];
//             final type = item['type'] as String?;
//             switch (type) {
//               case 'image':
//                 return ImagePostView(imageUrl: item['mediaUrl'] as String);
//               case 'video':
//                 return VideoPostView(videoUrl: item['mediaUrl'] as String);
//               case 'voice_note':
//                 return VoiceNoteView(
//                   audioUrl: item['mediaUrl'] as String,
//                   title: item['title'] as String,
//                 );
//               default:
//                 return Container(
//                   color: Colors.grey,
//                   child: const Center(child: Text('Unsupported format')),
//                 );
//             }
//           },
//         ),
//         Positioned(
//           bottom: 20,
//           right: 10,
//           child: Column(
//             children: [
//               IconButton(
//                 icon: Icon(
//                   _isLiked ? EvaIcons.arrowUpward : EvaIcons.arrowUpwardOutline,
//                   color: _isLiked ? Colors.teal : Colors.white,
//                   size: 30,
//                 ),
//                 onPressed: _onLike,
//               ),
//               Text(
//                 _likeCount.toString(),
//                 style: const TextStyle(
//                   color: Colors.white,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               const SizedBox(height: 16),
//               IconButton(
//                 icon: const Icon(
//                   EvaIcons.messageSquare,
//                   color: Colors.white,
//                   size: 30,
//                 ),
//                 onPressed: _onComment,
//               ),
//               // --- THE FIX: Display the comment count ---
//               Text(
//                 _commentCount.toString(),
//                 style: const TextStyle(
//                   color: Colors.white,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               // --- End of FIX ---
//               const SizedBox(height: 16),
//               IconButton(
//                 icon: const Icon(
//                   EvaIcons.bookmark,
//                   color: Colors.white,
//                   size: 30,
//                 ),
//                 onPressed: _onSave,
//               ),
//               const Text("Save", style: TextStyle(color: Colors.white)),
//               const SizedBox(height: 16),
//               IconButton(
//                 icon: const Icon(EvaIcons.share, color: Colors.white, size: 30),
//                 onPressed: _onShare,
//               ),
//               const Text("Share", style: TextStyle(color: Colors.white)),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:wicker/services/post_service.dart';
import 'package:wicker/widgets/add_to_playlist_modal.dart';
import 'package:wicker/widgets/comments_modal.dart';
import 'image_post_view.dart';
import 'video_post_view.dart';
import 'voice_note_view.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class CategoryFeedView extends StatefulWidget {
  final int initialIndex;
  final List<Map<String, String>> items;

  const CategoryFeedView({
    super.key,
    required this.items,
    required this.initialIndex,
  });

  @override
  _CategoryFeedViewState createState() => _CategoryFeedViewState();
}

class _CategoryFeedViewState extends State<CategoryFeedView>
    with AutomaticKeepAliveClientMixin {
  late PageController _pageController;
  late int _currentIndex;
  final PostService _postService = PostService();

  // State for likes/dislikes - now using a map to track per item
  final Map<int, bool> _likedStates = {};
  final Map<int, bool> _dislikedStates = {};
  final Map<int, int> _likeCounts = {};

  @override
  bool get wantKeepAlive => true; // Prevent widget from being disposed

  @override
  void initState() {
    super.initState();

    // Validate and clamp the initial index
    final clampedIndex = widget.initialIndex.clamp(0, widget.items.length - 1);
    _currentIndex = clampedIndex;

    // Initialize PageController with proper bounds checking
    _pageController = PageController(initialPage: _currentIndex);

    // Initialize like states for all items
    _initializeLikeStates();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // Initialize like states for all items to prevent null reference errors
  void _initializeLikeStates() {
    for (int i = 0; i < widget.items.length; i++) {
      final item = widget.items[i];
      _likedStates[i] = item['isLiked'] == 'true';
      _dislikedStates[i] = item['isDisliked'] == 'true';
      _likeCounts[i] = int.tryParse(item['likes'] ?? '0') ?? 0;
    }
  }

  // Get current item's like state safely
  bool get _isLiked => _likedStates[_currentIndex] ?? false;
  bool get _isDisliked => _dislikedStates[_currentIndex] ?? false;
  int get _likeCount => _likeCounts[_currentIndex] ?? 0;

  // --- Action Button Handlers ---
  void _onLike() {
    if (_currentIndex >= widget.items.length) return; // Safety check

    final postId = widget.items[_currentIndex]['_id'];
    if (postId == null || postId.isEmpty) return; // Safety check

    setState(() {
      if (_likedStates[_currentIndex] ?? false) {
        _likedStates[_currentIndex] = false;
        _likeCounts[_currentIndex] = (_likeCounts[_currentIndex] ?? 0) - 1;
      } else {
        _likedStates[_currentIndex] = true;
        _likeCounts[_currentIndex] = (_likeCounts[_currentIndex] ?? 0) + 1;
        _dislikedStates[_currentIndex] = false; // A like cancels a dislike
      }
    });

    // Call API after state update to prevent blocking UI
    Future.microtask(() => _postService.likePost(postId));
  }

  void _onComment() {
    if (_currentIndex >= widget.items.length) return; // Safety check

    final postId = widget.items[_currentIndex]['_id'];
    if (postId == null || postId.isEmpty) return; // Safety check

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => FractionallySizedBox(
        heightFactor: 0.9,
        child: CommentsModal(postId: postId),
      ),
    );
  }

  void _onSave() {
    if (_currentIndex >= widget.items.length) return; // Safety check

    final currentItem = widget.items[_currentIndex];
    final itemId = currentItem['_id'];
    final itemType = currentItem['type'];

    if (itemId == null || itemId.isEmpty || itemType == null) {
      return; // Safety check
    }

    showModalBottomSheet(
      context: context,
      builder: (context) =>
          AddToPlaylistModal(itemId: itemId, itemType: itemType),
    );
  }

  // Build the media widget based on type
  Widget _buildMediaWidget(Map<String, String> item, int index) {
    final type = item['type'] ?? 'unknown';
    final mediaUrl = item['mediaUrl'] ?? '';
    final title = item['title'] ?? '';

    // Add a unique key to prevent widget recycling issues
    final key = ValueKey('${item['_id']}_$index');

    switch (type) {
      case 'image':
        return ImagePostView(key: key, imageUrl: mediaUrl);
      case 'video':
        return VideoPostView(key: key, videoUrl: mediaUrl);
      case 'voice_note':
        return VoiceNoteView(key: key, audioUrl: mediaUrl, title: title);
      case 'text':
        return Container(
          key: key,
          color: Colors.grey[900],
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Text(
                title,
                style: const TextStyle(color: Colors.white, fontSize: 18),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      default:
        return Container(
          key: key,
          color: Colors.grey,
          child: Center(
            child: Text(
              'Unknown format: $type',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Safety check for empty items
    if (widget.items.isEmpty) {
      return const Scaffold(body: Center(child: Text('No items to display')));
    }

    return Scaffold(
      body: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            scrollDirection: Axis.vertical,
            onPageChanged: (index) {
              // Add bounds checking
              if (index >= 0 && index < widget.items.length) {
                setState(() {
                  _currentIndex = index;
                });
              }
            },
            itemCount: widget.items.length,
            itemBuilder: (context, index) {
              // Add bounds checking
              if (index >= widget.items.length) {
                return Container(
                  color: Colors.grey,
                  child: const Center(child: Text('Error: Invalid index')),
                );
              }

              final item = widget.items[index];
              return _buildMediaWidget(item, index);
            },
          ),

          Positioned(
            bottom: 20,
            right: 10,
            child: Column(
              children: [
                IconButton(
                  icon: Icon(
                    _isLiked
                        ? EvaIcons.arrowUpward
                        : EvaIcons.arrowUpwardOutline,
                    color: _isLiked ? Colors.teal : Colors.white,
                    size: 30,
                  ),
                  onPressed: _onLike,
                ),
                Text(
                  _likeCount.toString(),
                  style: const TextStyle(color: Colors.white),
                ),
                const SizedBox(height: 16),
                IconButton(
                  icon: const Icon(
                    EvaIcons.messageSquare,
                    color: Colors.white,
                    size: 30,
                  ),
                  onPressed: _onComment,
                ),
                const Text("Comment", style: TextStyle(color: Colors.white)),
                const SizedBox(height: 16),
                IconButton(
                  icon: const Icon(
                    EvaIcons.bookmark,
                    color: Colors.white,
                    size: 30,
                  ),
                  onPressed: _onSave,
                ),
                const Text("Save", style: TextStyle(color: Colors.white)),
                const SizedBox(height: 16),
                IconButton(
                  icon: const Icon(
                    EvaIcons.share,
                    color: Colors.white,
                    size: 30,
                  ),
                  onPressed: () {
                    // TODO: Implement share
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Share feature coming soon!'),
                      ),
                    );
                  },
                ),
                const Text("Share", style: TextStyle(color: Colors.white)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
