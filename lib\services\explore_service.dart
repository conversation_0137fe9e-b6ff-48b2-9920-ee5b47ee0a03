import 'dart:convert';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class ExploreService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<List<Map<String, dynamic>>> searchBusinesses(String query) async {
    if (query.isEmpty) return [];

    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse(
        '$baseUrl/api/ecommerce/business/search',
      ).replace(queryParameters: {'q': query}),
    );
    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data);
    }
    throw Exception('Failed to search businesses');
  }
}
