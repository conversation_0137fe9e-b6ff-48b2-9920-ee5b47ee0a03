#!/bin/bash

# Bash script to update IP addresses in the Flutter project
# Usage: ./scripts/update_ip.sh [ip_address]

set -e

echo "🔧 Wicker IP Address Updater (Bash)"
echo "==================================="

# Function to validate IP address
validate_ip() {
    local ip=$1
    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        IFS='.' read -ra ADDR <<< "$ip"
        for i in "${ADDR[@]}"; do
            if [[ $i -gt 255 || $i -lt 0 ]]; then
                return 1
            fi
        done
        return 0
    else
        return 1
    fi
}

# Function to test server connectivity
test_server() {
    local ip=$1
    local port=${2:-5000}
    local timeout=${3:-2}
    
    if command -v nc >/dev/null 2>&1; then
        # Use netcat if available
        nc -z -w$timeout "$ip" "$port" >/dev/null 2>&1
    elif command -v timeout >/dev/null 2>&1; then
        # Use timeout with /dev/tcp if available
        timeout $timeout bash -c "echo >/dev/tcp/$ip/$port" >/dev/null 2>&1
    else
        # Fallback: assume connection works
        return 0
    fi
}

# Function to auto-discover server IP
auto_discover() {
    echo "🔍 Auto-discovering server IP address..."
    
    # Get current device IP to determine network range
    local device_ip
    if command -v ip >/dev/null 2>&1; then
        device_ip=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null)
    elif command -v ifconfig >/dev/null 2>&1; then
        device_ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi
    
    local network_ranges=()
    
    if [[ -n $device_ip ]]; then
        local network_base=$(echo "$device_ip" | cut -d. -f1-3)
        network_ranges+=("$network_base")
    fi
    
    # Add common network ranges
    network_ranges+=("192.168.1" "192.168.0" "192.168.8" "10.0.0" "172.16.0")
    
    for range in "${network_ranges[@]}"; do
        local common_ips=("$range.1" "$range.100" "$range.101" "$range.107" "$range.219")
        
        for ip in "${common_ips[@]}"; do
            echo "   Testing $ip..."
            if test_server "$ip"; then
                echo "✅ Found server at: $ip"
                echo "$ip"
                return 0
            fi
        done
    done
    
    return 1
}

# Main logic
NEW_IP=""

if [[ $# -eq 0 ]]; then
    if NEW_IP=$(auto_discover); then
        echo "📝 Using discovered IP: $NEW_IP"
    else
        echo "❌ Could not auto-discover server IP."
        echo "💡 Usage: ./scripts/update_ip.sh <ip_address>"
        echo "   Example: ./scripts/update_ip.sh *************"
        exit 1
    fi
else
    NEW_IP=$1
    echo "📝 Using provided IP: $NEW_IP"
fi

# Validate IP format
if ! validate_ip "$NEW_IP"; then
    echo "❌ Invalid IP address format: $NEW_IP"
    exit 1
fi

echo "🔄 Updating IP addresses in project files..."

# Files to update
FILES=(
    "lib/services/auth_service.dart"
    "lib/services/post_service.dart"
    "lib/services/places_service.dart"
    "lib/services/queue_service.dart"
    "lib/services/playlist_service.dart"
    "lib/screens/home_screen.dart"
    "lib/screens/explore_screen.dart"
    "lib/widgets/post_card.dart"
    "lib/widgets/place_detail_card.dart"
    "lib/widgets/user_contributions_tab.dart"
)

UPDATED_FILES=0

for file in "${FILES[@]}"; do
    if [[ -f "$file" ]]; then
        # Create backup
        cp "$file" "$file.bak"
        
        # Update IP addresses (excluding localhost)
        sed -i.tmp \
            -e "s|\"http://[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}:5000\"|\"http://$NEW_IP:5000\"|g" \
            -e "s|\"[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\"|\"$NEW_IP\"|g" \
            "$file"
        
        # Restore localhost if it was accidentally replaced
        sed -i.tmp2 \
            -e "s|\"http://$NEW_IP:5000\"|\"http://127.0.0.1:5000\"|g" \
            -e "s|\"$NEW_IP\"|\"127.0.0.1\"|g" \
            "$file"
        
        # Re-apply Android IP replacement
        sed -i.tmp3 \
            -e "s|defaultTargetPlatform == TargetPlatform\.android[[:space:]]*?[[:space:]]*\"http://127\.0\.0\.1:5000\"|defaultTargetPlatform == TargetPlatform.android ? \"http://$NEW_IP:5000\"|g" \
            -e "s|defaultTargetPlatform == TargetPlatform\.android[[:space:]]*?[[:space:]]*\"127\.0\.0\.1\"|defaultTargetPlatform == TargetPlatform.android ? \"$NEW_IP\"|g" \
            "$file"
        
        # Clean up temporary files
        rm -f "$file.tmp" "$file.tmp2" "$file.tmp3"
        
        # Check if file was actually changed
        if ! cmp -s "$file" "$file.bak"; then
            echo "✅ Updated: $file"
            ((UPDATED_FILES++))
        else
            echo "ℹ️  No changes needed: $file"
        fi
        
        # Remove backup
        rm -f "$file.bak"
    else
        echo "⚠️  File not found: $file"
    fi
done

# Update summary document
TIMESTAMP=$(date -Iseconds)
cat > "IP_ADDRESS_UPDATE_SUMMARY.md" << EOF
# IP Address Update Summary

## Latest Update: $TIMESTAMP

**Current IP Address: $NEW_IP**

## Overview

Updated all IP addresses throughout the Flutter application to use \`http://$NEW_IP:5000\` for Android physical device testing, while maintaining \`http://127.0.0.1:5000\` for web and iOS platforms.

## Auto-Update Scripts

This project includes automatic IP update scripts:

\`\`\`bash
# Dart script (cross-platform)
dart scripts/update_ip.dart

# Bash script (Unix/Linux/macOS)
./scripts/update_ip.sh

# PowerShell script (Windows)
.\scripts\update_ip.ps1

# Update to specific IP
dart scripts/update_ip.dart *************
./scripts/update_ip.sh *************
.\scripts\update_ip.ps1 *************
\`\`\`

## Platform Detection Strategy

All services use the following pattern for cross-platform compatibility:

\`\`\`dart
final String _baseUrl = defaultTargetPlatform == TargetPlatform.android
    ? "http://$NEW_IP:5000"  // Physical Android device
    : "http://127.0.0.1:5000";      // Web/iOS/Desktop
\`\`\`

## Network Configuration

The app is configured to work with:
- **Android Physical Device**: \`http://$NEW_IP:5000\`
- **Web/iOS/Desktop**: \`http://127.0.0.1:5000\`
- **Android Manifest**: Already configured with \`android:usesCleartextTraffic="true"\` for HTTP traffic

## Next Steps for Testing

1. Ensure the backend server is running on \`$NEW_IP:5000\`
2. Connect Android device to the same network
3. Build and install the app on the physical device:
   \`\`\`bash
   flutter build apk --debug
   flutter install
   \`\`\`
4. Test all network-dependent features (authentication, posts, places, etc.)

---
*This document was automatically updated by the IP update script.*
EOF

echo "✅ Updated: IP_ADDRESS_UPDATE_SUMMARY.md"

echo ""
echo "📊 Summary:"
echo "   Files updated: $UPDATED_FILES"
echo "   New IP address: $NEW_IP"
echo ""
echo "🚀 Next steps:"
echo "   1. Run: flutter pub get"
echo "   2. Restart your app"
echo "   3. Test connectivity to http://$NEW_IP:5000"

# Make the script executable
chmod +x "$0" 2>/dev/null || true
