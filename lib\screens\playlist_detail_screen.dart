import 'package:flutter/material.dart';
import 'package:wicker/services/playlist_service.dart';
import 'package:wicker/widgets/post_card.dart'; // We can reuse the PostCard

class PlaylistDetailScreen extends StatefulWidget {
  final String playlistId;
  final String playlistName;

  const PlaylistDetailScreen({
    super.key,
    required this.playlistId,
    required this.playlistName,
  });

  @override
  State<PlaylistDetailScreen> createState() => _PlaylistDetailScreenState();
}

class _PlaylistDetailScreenState extends State<PlaylistDetailScreen> {
  final PlaylistService _playlistService = PlaylistService();
  late Future<Map<String, dynamic>> _playlistFuture;

  @override
  void initState() {
    super.initState();
    _playlistFuture = _playlistService.getPlaylistDetails(widget.playlistId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.playlistName)),
      body: FutureBuilder<Map<String, dynamic>>(
        future: _playlistFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData) {
            return const Center(child: Text('Could not load playlist details.'));
          }

          final items = snapshot.data?['populated_items'] as List<dynamic>? ?? [];

          if (items.isEmpty) {
            return const Center(child: Text('This playlist is empty.'));
          }

          return ListView.builder(
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index] as Map<String, dynamic>;

              // Process the data to fit the PostCard's expected format
              final postData = _processItemForCard(item);

              // We can reuse our existing PostCard to display the items
              return PostCard(postData: postData);
            },
          );
        },
      ),
    );
  }

  // Helper to adapt both Posts and Places to the PostCard format
  Map<String, dynamic> _processItemForCard(Map<String, dynamic> item) {
      // Add default values and map fields
      item['title'] = item['text_content'] ?? item['name'] ?? 'Untitled';
      item['media'] = item['media'] ?? item['photos'] ?? [];

      // Add other necessary fields for the PostCard
      // In a real app, you might want to fetch author details for places too
      if (item['author_details'] != null) {
          final authorDetails = item['author_details'] as Map<String, dynamic>;
          item['posterName'] = authorDetails['username'] ?? 'A User';
          item['avatarUrl'] = 'https://picsum.photos/seed/${authorDetails['_id']['\$oid']}/100';
      } else {
          item['posterName'] = 'Wicker Place';
          item['avatarUrl'] = 'https://picsum.photos/seed/place/100';
      }

      item['views'] = '0';
      item['postedTime'] = '...';

      return item;
  }
}