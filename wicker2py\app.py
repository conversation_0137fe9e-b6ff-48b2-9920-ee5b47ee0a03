import os
from flask import Flask, jsonify, send_from_directory
from dotenv import load_dotenv
import pymongo
import openai
from flask_bcrypt import Bcrypt
from flask_jwt_extended import JWTManager
from api.auth import auth_bp 
from api.places import places_bp 
from api.playlists import playlists_bp 
from api.queues import queues_bp
from api.posts import posts_bp
from api.users import users_bp
from api.reports import reports_bp
from api.comments import comments_bp
from api.ecommerce import ecommerce_bp
from api.socials import socials_bp
from flask_cors import CORS

# Load environment variables from .env file
load_dotenv()

# --- App Initialization ---
app = Flask(__name__)
bcrypt = Bcrypt(app)
jwt = JWTManager(app)
CORS(app)  # Enable CORS for all routes

# --- Configurations ---
MONGO_URI = os.getenv('MONGO_URI')
DB_NAME = os.getenv('DB')
app.config["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')
app.config["JWT_SECRET_KEY"] = os.getenv('JWT_SECRET_KEY') # Add this


# --- Database & Services Setup ---
try:
    mongo_client = pymongo.MongoClient(MONGO_URI)
    db = mongo_client[DB_NAME]
    mongo_client.admin.command('ping')
    print("✅ MongoDB connection successful.")

    # Set up OpenAI client
    # openai.api_key = app.config["OPENAI_API_KEY"]
    openai_client = openai.OpenAI(api_key=app.config["OPENAI_API_KEY"])
    app.openai_client = openai_client
    print("✅ OpenAI client configured.")
except Exception as e:
    print(f"❌ Error during initialization: {e}")
    db = None

# --- Register Blueprints ---
# Pass the db instance to the blueprint
if db is not None:
    auth_bp.db = db
    places_bp.db = db
    playlists_bp.db = db 
    queues_bp.db = db
    posts_bp.db = db
    users_bp.db = db
    reports_bp.db = db
    comments_bp.db = db
    ecommerce_bp.db = db
    socials_bp.db = db
  


    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(places_bp, url_prefix='/api/places')
    app.register_blueprint(playlists_bp, url_prefix='/api/playlists')
    app.register_blueprint(queues_bp, url_prefix='/api/queues')
    app.register_blueprint(posts_bp, url_prefix='/api/posts') 
    app.register_blueprint(users_bp, url_prefix='/api/users')
    app.register_blueprint(reports_bp, url_prefix='/api/reports')
    app.register_blueprint(comments_bp, url_prefix='/api/comments')
    app.register_blueprint(ecommerce_bp, url_prefix='/api/ecommerce')
    app.register_blueprint(socials_bp, url_prefix='/api/socials')


    print("✅ Blueprints registered successfully.")

else:
    print("❌ Could not register blueprints, DB connection failed.")

# --- Routes ---
@app.route('/')
def index():
    return jsonify({"status": "ok", "message": "Welcome to the Wicker API!"})

@app.route('/uploads/<path:filename>')
def uploaded_file(filename):
    """Serves a file from the uploads directory."""
    return send_from_directory('uploads', filename)


# --- Main Execution ---
if __name__ == '__main__':
    app.run(debug=True)

