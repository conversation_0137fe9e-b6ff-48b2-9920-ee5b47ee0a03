# from flask import Response, request, jsonify, Blueprint
# from flask_jwt_extended import jwt_required, get_jwt_identity
# from bson import ObjectId, json_util
# import datetime

# playlists_bp = Blueprint('playlists_bp', __name__)

# # In api/playlists.py

# # In api/playlists.py

# @playlists_bp.route('/<playlist_id>', methods=['GET'])
# @jwt_required()
# def get_playlist_details(playlist_id):
#     db = playlists_bp.db
#     playlists_collection = db.playlists
    
#     try:
#         # THE FIX: First, find the playlist to ensure it exists.
#         playlist = playlists_collection.find_one({'_id': ObjectId(playlist_id)})

#         if not playlist:
#             return jsonify({"msg": "Playlist not found"}), 404

#         # If the playlist exists but is empty, we can return it as is.
#         if not playlist.get('items'):
#             playlist['populated_items'] = []
#             return Response(json_util.dumps(playlist), mimetype='application/json')
        
#         # If it has items, proceed with the aggregation to populate them.
#         pipeline = [
#             {'$match': {'_id': ObjectId(playlist_id)}},
#             {'$unwind': '$items'},
#             {
#                 '$lookup': {
#                     'from': 'posts',
#                     'localField': 'items.item_id',
#                     'foreignField': '_id',
#                     'as': 'populated_post'
#                 }
#             },
#             {
#                 '$lookup': {
#                     'from': 'places',
#                     'localField': 'items.item_id',
#                     'foreignField': '_id',
#                     'as': 'populated_place'
#                 }
#             },
#             {
#                 '$addFields': {
#                     'populated_item': {'$ifNull': [{'$arrayElemAt': ['$populated_post', 0]}, {'$arrayElemAt': ['$populated_place', 0]}]}
#                 }
#             },
#             {
#                 '$group': {
#                     '_id': '$_id',
#                     'name': {'$first': '$name'},
#                     'owner_id': {'$first': '$owner_id'},
#                     'is_private': {'$first': '$is_private'},
#                     'items': {'$push': '$items'},
#                     'populated_items': {'$push': '$populated_item'}
#                 }
#             }
#         ]
        
#         result = list(playlists_collection.aggregate(pipeline))
        
#         return Response(json_util.dumps(result[0]), mimetype='application/json')
#     except Exception as e:
#         return jsonify({"msg": "An error occurred", "error": str(e)}), 500



#     # NEW: Route to get all playlists for the current user
# @playlists_bp.route('/', methods=['GET', 'POST'])
# @jwt_required()
# def get_my_playlists():
#     db = playlists_bp.db
#     playlists_collection = db.playlists
#     current_user_id = get_jwt_identity()

    
#     try:
#         user_playlists = list(playlists_collection.find({
#             "owner_id": ObjectId(current_user_id)
#         }))
#         return Response(
#             json_util.dumps(user_playlists),
#             mimetype='application/json'
#         )
#     except Exception as e:
#         return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    



# # In api/playlists.py

# # In api/playlists.py

# @playlists_bp.route('/<playlist_id>/add-item', methods=['POST'])
# @jwt_required()
# def add_item_to_playlist(playlist_id):
#     db = playlists_bp.db
#     playlists_collection = db.playlists
#     current_user_id = ObjectId(get_jwt_identity())
#     data = request.get_json()

#     item_id = data.get('item_id')
#     item_type = data.get('item_type')

#     if not item_id or not item_type:
#         return jsonify({"msg": "item_id and item_type are required"}), 400

#     # THE FIX: Ensure the query to find the playlist is correct
#     playlist_to_update = {
#         '_id': ObjectId(playlist_id),
#         'owner_id': current_user_id
#     }

#     # The new item to be added
#     new_item = {
#         'item_id': ObjectId(item_id),
#         'item_type': item_type,
#         'custom_name': data.get('custom_name'),
#         'added_at': datetime.datetime.now(datetime.timezone.utc)
#     }

#     # Update the found playlist by pushing the new item
#     result = playlists_collection.update_one(
#         playlist_to_update,
#         {'$push': {'items': new_item}}
#     )

#     if result.modified_count == 0:
#         return jsonify({"msg": "Playlist not found or you don't have permission"}), 404
    
#     return jsonify({"msg": "Item added to playlist"}), 200





from flask import Response, request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime

playlists_bp = Blueprint('playlists_bp', __name__)

# --- NEW: Route and function to create a new playlist ---
@playlists_bp.route('/create', methods=['POST'])
@jwt_required()
def create_playlist():
    """
    Creates a new playlist for the currently logged-in user.
    """
    db = playlists_bp.db
    playlists_collection = db.playlists
    current_user_id = get_jwt_identity()
    data = request.get_json()

    # --- Input Validation ---
    if not data or not data.get('name'):
        return jsonify({"msg": "Playlist name is required"}), 400

    # --- Create Playlist Document ---
    new_playlist = {
        "name": data.get('name'),
        "is_private": data.get('is_private', True), # Default to private
        "owner_id": ObjectId(current_user_id),
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "items": [] # Start with an empty list of items
    }
    
    # --- Insert into Database ---
    try:
        playlists_collection.insert_one(new_playlist)
        return jsonify({"msg": "Playlist created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

# --- Existing routes below are unchanged ---

@playlists_bp.route('/<playlist_id>', methods=['GET'])
@jwt_required()
def get_playlist_details(playlist_id):
    db = playlists_bp.db
    playlists_collection = db.playlists
    
    try:
        # First, find the playlist to ensure it exists.
        playlist = playlists_collection.find_one({'_id': ObjectId(playlist_id)})

        if not playlist:
            return jsonify({"msg": "Playlist not found"}), 404

        # If the playlist exists but is empty, we can return it as is.
        if not playlist.get('items'):
            playlist['populated_items'] = []
            return Response(json_util.dumps(playlist), mimetype='application/json')
        
        # If it has items, proceed with the aggregation to populate them.
        pipeline = [
            {'$match': {'_id': ObjectId(playlist_id)}},
            {'$unwind': '$items'},
            {
                '$lookup': {
                    'from': 'posts',
                    'localField': 'items.item_id',
                    'foreignField': '_id',
                    'as': 'populated_post'
                }
            },
            {
                '$lookup': {
                    'from': 'places',
                    'localField': 'items.item_id',
                    'foreignField': '_id',
                    'as': 'populated_place'
                }
            },
            {
                '$addFields': {
                    'populated_item': {'$ifNull': [{'$arrayElemAt': ['$populated_post', 0]}, {'$arrayElemAt': ['$populated_place', 0]}]}
                }
            },
            {
                '$group': {
                    '_id': '$_id',
                    'name': {'$first': '$name'},
                    'owner_id': {'$first': '$owner_id'},
                    'is_private': {'$first': '$is_private'},
                    'items': {'$push': '$items'},
                    'populated_items': {'$push': '$populated_item'}
                }
            }
        ]
        
        result = list(playlists_collection.aggregate(pipeline))
        
        return Response(json_util.dumps(result[0]), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

@playlists_bp.route('/', methods=['GET'])
@jwt_required()
def get_my_playlists():
    db = playlists_bp.db
    playlists_collection = db.playlists
    current_user_id = get_jwt_identity()

    try:
        user_playlists = list(playlists_collection.find({
            "owner_id": ObjectId(current_user_id)
        }))
        return Response(
            json_util.dumps(user_playlists),
            mimetype='application/json'
        )
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

@playlists_bp.route('/<playlist_id>/add-item', methods=['POST'])
@jwt_required()
def add_item_to_playlist(playlist_id):
    db = playlists_bp.db
    playlists_collection = db.playlists
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    item_id = data.get('item_id')
    item_type = data.get('item_type')

    if not item_id or not item_type:
        return jsonify({"msg": "item_id and item_type are required"}), 400

    playlist_to_update = {
        '_id': ObjectId(playlist_id),
        'owner_id': current_user_id
    }

    new_item = {
        'item_id': ObjectId(item_id),
        'item_type': item_type,
        'custom_name': data.get('custom_name'),
        'added_at': datetime.datetime.now(datetime.timezone.utc)
    }

    result = playlists_collection.update_one(
        playlist_to_update,
        {'$push': {'items': new_item}}
    )

    if result.modified_count == 0:
        return jsonify({"msg": "Playlist not found or you don't have permission"}), 404
    
    return jsonify({"msg": "Item added to playlist"}), 200