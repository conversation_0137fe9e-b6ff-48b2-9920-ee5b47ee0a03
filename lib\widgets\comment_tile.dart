import 'package:flutter/material.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class CommentTile extends StatelessWidget {
  final Map<String, dynamic> commentData;
  final int depth;
  final Function(String, String) onReply;

  const CommentTile({
    super.key,
    required this.commentData,
    this.depth = 0,
    required this.onReply,
  });

  @override
  Widget build(BuildContext context) {
    final author = commentData['author_details'] as Map<String, dynamic>? ?? {};
    final username = author['username'] ?? 'Anonymous';
    final commentId = commentData['_id']['\$oid'];
    final avatarUrl = 'https://i.pravatar.cc/150?u=$commentId';

    return NeuCard(
      margin: EdgeInsets.only(
        left: 16.0 * depth, // Indent replies
        right: 16.0,
        top: 8.0,
        bottom: 8.0,
      ),
      padding: const EdgeInsets.all(12.0),
      backgroundColor: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 18,
                backgroundImage: NetworkImage(avatarUrl),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  username,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(left: 48.0), // Align with username
            child: Text(commentData['comment_text'] ?? ''),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: () => onReply(commentId, username),
                child: const NeuCard(
                  margin: EdgeInsets.zero,
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  shadowOffset: 2.0,
                  borderWidth: 2.0,
                  child: Text(
                    'Reply',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
