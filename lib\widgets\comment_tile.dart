import 'package:flutter/material.dart';
import 'package:wicker/services/comment_service.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';

class CommentTile extends StatefulWidget {
  final Map<String, dynamic> commentData;
  final int depth;
  final Function(String, String) onReply;

  const CommentTile({
    super.key,
    required this.commentData,
    this.depth = 0,
    required this.onReply,
  });

  @override
  State<CommentTile> createState() => _CommentTileState();
}

class _CommentTileState extends State<CommentTile> {
  final CommentService _commentService = CommentService();
  late bool _isLiked;
  late int _likeCount;

  @override
  void initState() {
    super.initState();
    _isLiked =
        (widget.commentData['likes'] as List?)?.contains(
          /* TODO: Get current user ID */
          '',
        ) ??
        false;
    _likeCount = (widget.commentData['likes'] as List?)?.length ?? 0;
  }

  void _onLike() {
    final commentId = widget.commentData['_id']['\$oid'];
    setState(() {
      _isLiked = !_isLiked;
      _likeCount += _isLiked ? 1 : -1;
    });
    _commentService.likeComment(commentId).catchError((e) {
      // Revert on error
      setState(() {
        _isLiked = !_isLiked;
        _likeCount -= _isLiked ? 1 : -1;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final author =
        widget.commentData['author_details'] as Map<String, dynamic>? ?? {};
    final username = author['username'] ?? 'Anonymous';
    final commentId = widget.commentData['_id']['\$oid'];

    return NeuCard(
      margin: EdgeInsets.only(
        left: 16.0 * widget.depth,
        right: 16.0,
        top: 8.0,
        bottom: 8.0,
      ),
      padding: const EdgeInsets.all(12.0),
      backgroundColor: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              FutureBuilder<String>(
                future: ConfigService.instance.getBaseUrl(),
                builder: (context, snapshot) {
                  String avatarUrl =
                      'https://i.pravatar.cc/150?u=$commentId'; // Fallback
                  String? picPath = author['profile_pic_url']?.toString();

                  if (snapshot.hasData &&
                      picPath != null &&
                      picPath.isNotEmpty) {
                    final baseUrl = snapshot.data!;
                    avatarUrl = '$baseUrl/${picPath.replaceAll('\\', '/')}';
                  }

                  return CircleAvatar(
                    radius: 18,
                    backgroundImage: NetworkImage(avatarUrl),
                  );
                },
              ),
              // --- End of FIX ---
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  username,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          Padding(
            padding: const EdgeInsets.only(left: 48.0),
            child: Text(widget.commentData['comment_text'] ?? ''),
          ),
          const SizedBox(height: 4),

          // --- NEW: Action Buttons for Comments ---
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton.icon(
                onPressed: _onLike,
                icon: Icon(
                  _isLiked ? EvaIcons.heart : EvaIcons.heartOutline,
                  color: _isLiked ? Colors.red : Colors.grey,
                  size: 16,
                ),
                label: Text(
                  _likeCount.toString(),
                  style: TextStyle(color: Colors.grey[700]),
                ),
              ),
              const SizedBox(width: 8),
              TextButton(
                onPressed: () => widget.onReply(commentId, username),
                child: const Text(
                  'Reply',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
