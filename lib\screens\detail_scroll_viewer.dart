import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:wicker/widgets/category_feed_view.dart';

class DetailScrollViewer extends StatefulWidget {
  // Keep the original data type to preserve structure
  final Map<String, List<Map<String, dynamic>>> allCategoriesData;
  final int initialCategoryIndex;
  final int initialItemIndex;

  const DetailScrollViewer({
    super.key,
    required this.allCategoriesData,
    required this.initialCategoryIndex,
    required this.initialItemIndex,
  });

  @override
  _DetailScrollViewerState createState() => _DetailScrollViewerState();
}

class _DetailScrollViewerState extends State<DetailScrollViewer>
    with AutomaticKeepAliveClientMixin {
  late PageController _pageController;
  int _currentCategoryIndex = 0;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Clamp the initial category index to prevent out-of-bounds
    final categoryKeys = widget.allCategoriesData.keys.toList();
    final clampedCategoryIndex = widget.initialCategoryIndex.clamp(
      0,
      categoryKeys.length - 1,
    );
    _currentCategoryIndex = clampedCategoryIndex;

    _pageController = PageController(initialPage: _currentCategoryIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // FIXED: Complete transformation method that handles all data properly
  List<Map<String, String>> _transformItemsForCategoryFeedView(
    List<Map<String, dynamic>> rawItems,
  ) {
    final String baseUrl = defaultTargetPlatform == TargetPlatform.android
        ? "http://192.168.8.107:5000"
        : "http://127.0.0.1:5000";

    return rawItems.map((item) {
      final contentType = item['content_type'];
      Map<String, String> processedItem = {};

      // Add the _id field - this is crucial for CategoryFeedView
      if (item['_id'] != null) {
        if (item['_id'] is Map) {
          processedItem['_id'] = item['_id']['\$oid']?.toString() ?? '';
        } else {
          processedItem['_id'] = item['_id'].toString();
        }
      } else {
        processedItem['_id'] = '';
      }

      if (contentType == 'post') {
        // Handle posts
        processedItem['title'] = item['text_content']?.toString() ?? '';

        final mediaList = item['media'] as List<dynamic>? ?? [];
        if (mediaList.isNotEmpty) {
          final firstMedia = mediaList.first as Map<String, dynamic>;
          final mediaPath = firstMedia['path']?.toString() ?? '';
          final correctedPath = mediaPath.replaceAll('\\', '/');
          processedItem['mediaUrl'] = '$baseUrl/$correctedPath';
          processedItem['type'] = firstMedia['type']?.toString() ?? 'unknown';
        } else {
          processedItem['mediaUrl'] = '';
          processedItem['type'] = 'text'; // For text-only posts
        }

        // Add like/dislike data for posts
        processedItem['likes'] = item['likes']?.toString() ?? '0';
        processedItem['isLiked'] = item['isLiked']?.toString() ?? 'false';
        processedItem['isDisliked'] = item['isDisliked']?.toString() ?? 'false';
      } else if (contentType == 'place') {
        // Handle places
        processedItem['title'] = item['name']?.toString() ?? '';

        final photoList = item['photos'] as List<dynamic>? ?? [];
        if (photoList.isNotEmpty) {
          final photoPath = photoList.first?.toString() ?? '';
          final correctedPath = photoPath.replaceAll('\\', '/');
          processedItem['mediaUrl'] = '$baseUrl/$correctedPath';
          processedItem['type'] = 'image'; // Places only have images
        } else {
          processedItem['mediaUrl'] = '';
          processedItem['type'] = 'unknown';
        }

        // Add default like data for places
        processedItem['likes'] = '0';
        processedItem['isLiked'] = 'false';
        processedItem['isDisliked'] = 'false';
      } else {
        // Fallback for unknown content types
        processedItem['title'] =
            item['name']?.toString() ??
            item['text_content']?.toString() ??
            'Unknown';
        processedItem['mediaUrl'] = '';
        processedItem['type'] = 'unknown';
        processedItem['likes'] = '0';
        processedItem['isLiked'] = 'false';
        processedItem['isDisliked'] = 'false';
      }

      return processedItem;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final categoryKeys = widget.allCategoriesData.keys.toList();

    // Safety check for empty data
    if (categoryKeys.isEmpty) {
      return const Scaffold(
        body: Center(child: Text('No categories available')),
      );
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        // Show current category in app bar with safety check
        title: Text(
          _currentCategoryIndex < categoryKeys.length
              ? categoryKeys[_currentCategoryIndex]
              : 'Category',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: categoryKeys.length,
        onPageChanged: (index) {
          // Update current category index with bounds checking
          if (index >= 0 && index < categoryKeys.length && mounted) {
            setState(() {
              _currentCategoryIndex = index;
            });
          }
        },
        itemBuilder: (context, index) {
          // Add bounds checking
          if (index >= categoryKeys.length) {
            return const Center(child: Text('Category not found'));
          }

          final categoryTitle = categoryKeys[index];
          final items = widget.allCategoriesData[categoryTitle] ?? [];

          // Safety check for empty category
          if (items.isEmpty) {
            return Center(
              child: Text(
                'No items in $categoryTitle',
                style: const TextStyle(color: Colors.white),
              ),
            );
          }

          // Ensure we don't go out of bounds for initial item index
          final initialItemIdx = (index == widget.initialCategoryIndex)
              ? widget.initialItemIndex.clamp(0, items.length - 1)
              : 0;

          // Transform the raw data into the format CategoryFeedView expects
          final transformedItems = _transformItemsForCategoryFeedView(items);

          // Add debug info with safety checks
          if (kDebugMode) {
            print(
              'Building category: $categoryTitle with ${transformedItems.length} items',
            );
            print('Initial item index: $initialItemIdx');
          }

          // Add unique key to prevent widget recycling issues
          return CategoryFeedView(
            key: ValueKey('category_$categoryTitle'),
            items: transformedItems,
            initialIndex: initialItemIdx,
          );
        },
      ),
    );
  }
}
