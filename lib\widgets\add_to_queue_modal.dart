import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:wicker/services/queue_service.dart';

class AddToQueueModal extends StatefulWidget {
  final String itemId;
  final String itemType; // 'post' or 'place'

  const AddToQueueModal({
    super.key,
    required this.itemId,
    required this.itemType,
  });

  @override
  _AddToQueueModalState createState() => _AddToQueueModalState();
}

class _AddToQueueModalState extends State<AddToQueueModal> {
  final QueueService _queueService = QueueService();
  List<Map<String, dynamic>> _userQueues = [];
  Map<String, dynamic>? _selectedQueue;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchUserQueues();
  }

  Future<void> _fetchUserQueues() async {
    try {
      final queues = await _queueService.getMyQueues();
      setState(() {
        _userQueues = queues;
        _isLoading = false;
      });
    } catch (e) {
      // Handle error
    }
  }

  void _saveItemToQueue() async {
    if (_selectedQueue == null) return;

    final queueId = _selectedQueue!['_id']['\$oid'];

    try {
      await _queueService.addItemToQueue(
        queueId: queueId,
        itemId: widget.itemId,
        itemType: widget.itemType,
      );
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Added to ${_selectedQueue!['name']}')),
      );
    } catch (e) {
      // Handle error
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20.0),
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Add to Queue', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                DropdownButtonFormField<Map<String, dynamic>>(
                  value: _selectedQueue,
                  hint: const Text('Choose a queue'),
                  onChanged: (value) => setState(() => _selectedQueue = value),
                  items: _userQueues.map((queue) {
                    return DropdownMenuItem(
                      value: queue,
                      child: Text(queue['name']),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 24),
                NeuTextButton(
                  enableAnimation: true,
                  onPressed: _saveItemToQueue,
                  buttonColor: Colors.teal,
                  text: const Text('Add to Queue', style: TextStyle(color: Colors.white)),
          
                ),
              ],
            ),
    );
  }
}