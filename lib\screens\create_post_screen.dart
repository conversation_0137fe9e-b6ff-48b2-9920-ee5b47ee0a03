import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/services/post_service.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:wicker/widgets/media_preview_tile.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class CreatePostScreen extends StatefulWidget {
  const CreatePostScreen({super.key});

  @override
  _CreatePostScreenState createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final _textController = TextEditingController();
  final PostService _postService = PostService();
  final ImagePicker _picker = ImagePicker();

  final List<XFile> _mediaFiles = [];
  bool _isLoading = false;

  Future<void> _pickMedia() async {
    final List<XFile> pickedFiles = await _picker.pickMultipleMedia();
    setState(() {
      _mediaFiles.addAll(pickedFiles);
    });
  }

  void _submitPost() async {
    if (_textController.text.isEmpty && _mediaFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add content or media to your post'),
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final message = await _postService.createPost(
        textContent: _textController.text,
        mediaFiles: _mediaFiles,
      );
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(message)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0), // Light beige background
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(EvaIcons.arrowBack, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'Create a New Post',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: GestureDetector(
                onTap: _isLoading ? null : _submitPost,
                child: NeuCard(
                  margin: const EdgeInsets.symmetric(vertical: 10),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  backgroundColor: const Color(0xFF00D2D3), // Cyan
                  shadowOffset: 4,
                  borderWidth: 2,
                  child: Center(
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Text(
                            'Post',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Text Input Area
            Expanded(
              child: NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: TextField(
                  controller: _textController,
                  maxLines: null,
                  expands: true,
                  style: const TextStyle(fontSize: 18),
                  decoration: const InputDecoration(
                    hintText: "What's on your mind?",
                    border: InputBorder.none,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Media Preview Area
            _buildMediaPreviews(),

            // Action Buttons
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: _pickMedia,
                    child: NeuCard(
                      margin: EdgeInsets.zero,
                      backgroundColor: const Color(0xFF4ECDC4), // Teal
                      child: const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(EvaIcons.imageOutline, color: Colors.white),
                            SizedBox(width: 8),
                            Text(
                              'Add Media',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                GestureDetector(
                  onTap: () {
                    /* TODO: Implement Tag Place */
                  },
                  child: const NeuCard(
                    margin: EdgeInsets.zero,
                    backgroundColor: Color(0xFFFF6B6B), // Coral
                    child: Padding(
                      padding: EdgeInsets.all(12.0),
                      child: Row(
                        children: [
                          Icon(EvaIcons.pinOutline, color: Colors.white),
                          SizedBox(width: 8),
                          Text(
                            'Tag Place',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaPreviews() {
    if (_mediaFiles.isEmpty) return const SizedBox.shrink();

    return NeuCard(
      margin: EdgeInsets.zero,
      padding: const EdgeInsets.all(8),
      child: SizedBox(
        height: 110,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: _mediaFiles.length,
          itemBuilder: (context, index) {
            final mediaFile = _mediaFiles[index];
            return MediaPreviewTile(
              mediaFile: mediaFile,
              onRemove: () {
                setState(() {
                  _mediaFiles.removeAt(index);
                });
              },
            );
          },
        ),
      ),
    );
  }
}
