from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
import datetime

reports_bp = Blueprint('reports_bp', __name__)

@reports_bp.route('/create', methods=['POST'])
@jwt_required()
def create_report():
    """
    Creates a new report for a piece of content (post or place).
    """
    db = reports_bp.db
    reports_collection = db.reports
    current_user_id = get_jwt_identity()
    data = request.get_json()

    item_id = data.get('item_id')
    item_type = data.get('item_type') # e.g., 'post' or 'place'
    reason = data.get('reason')

    if not all([item_id, item_type, reason]):
        return jsonify({"msg": "item_id, item_type, and reason are required"}), 400

    new_report = {
        "reporter_id": ObjectId(current_user_id),
        "item_id": ObjectId(item_id),
        "item_type": item_type,
        "reason": reason,
        "status": "pending", # To track moderation status
        "reported_at": datetime.datetime.now(datetime.timezone.utc),
    }

    try:
        reports_collection.insert_one(new_report)
        return jsonify({"msg": "Report submitted successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500