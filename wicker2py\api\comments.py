from flask import request, jsonify, Blueprint, Response, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime

comments_bp = Blueprint('comments_bp', __name__)

# --- THE FIX: The route parameter is now correctly named 'parent_id' ---
@comments_bp.route('/<parent_id>', methods=['GET'])
@jwt_required()
def get_comments(parent_id):
    """Fetches comments for a given parent (post or business),
    now including full author details."""
    db = comments_bp.db
    comments_collection = db.comments
    # This correctly reads the 'type' query parameter (e.g., ?type=business)
    parent_type = request.args.get('type', 'post')
    
    match_query = {f"{parent_type}_id": ObjectId(parent_id)}

    try:
        pipeline = [
            {'$match': match_query},
            {'$sort': {'created_at': -1}},
            {
                '$lookup': {
                    'from': 'users',
                    'localField': 'author_id',
                    'foreignField': '_id',
                    'as': 'author_details'
                }
            },
            {'$unwind': '$author_details'},
            {'$project': {'author_details.password_hash': 0, 'author_details.email': 0}}
        ]
        
        comments = list(comments_collection.aggregate(pipeline))
        return Response(json_util.dumps(comments), mimetype='application/json')
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
# --- End of FIX ---


@comments_bp.route('/create', methods=['POST'])
@jwt_required()
def create_comment():
    db = comments_bp.db
    openai_client = current_app.openai_client
    comments_collection = db.comments
    current_user_id = get_jwt_identity()
    data = request.get_json()

    comment_text = data.get('comment_text')
    if not comment_text:
        return jsonify({"msg": "comment_text is required"}), 400

    sentiment = get_sentiment(openai_client, comment_text)
    
    new_comment = {
        "author_id": ObjectId(current_user_id),
        "comment_text": comment_text,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
        "likes": [],
        "dislikes": [],
        "sentiment": sentiment
    }

    # --- THE FIX: Check for and add parent_id for replies ---
    if 'parent_id' in data and data['parent_id']:
        new_comment['parent_id'] = ObjectId(data['parent_id'])
    # --- End of FIX ---

    # REFACTORED: Add either a post_id or a business_id
    if 'post_id' in data:
        new_comment['post_id'] = ObjectId(data['post_id'])
    elif 'business_id' in data:
        new_comment['business_id'] = ObjectId(data['business_id'])
    else:
        return jsonify({"msg": "A post_id or a business_id is required"}), 400

    try:
        result = comments_collection.insert_one(new_comment)
        return jsonify({"msg": "Comment posted successfully", "comment_id": str(result.inserted_id)}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500




def get_sentiment(openai_client, comment_text):
    """Uses OpenAI to classify the sentiment of a comment."""
    try:
        prompt = f"""
        Classify the sentiment of the following comment as 'positive', 'negative', or 'neutral'.
        Respond with only one of those three words.
        Comment: "{comment_text}"
        """
        completion = openai_client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}]
        )
        sentiment = completion.choices[0].message.content.lower().strip()
        return sentiment if sentiment in ['positive', 'negative', 'neutral'] else 'neutral'
    except Exception as e:
        print(f"Sentiment analysis failed: {e}")
        return 'neutral' # Default to neutral on failure

@comments_bp.route('/<comment_id>/like', methods=['POST'])
@jwt_required()
def like_comment(comment_id):
    """Toggles a like on a comment for the current user."""
    db = comments_bp.db
    comments_collection = db.comments
    current_user_id = get_jwt_identity()

    try:
        comment_oid = ObjectId(comment_id)
        user_oid = ObjectId(current_user_id)

        # Check if the user has already liked the comment
        comment = comments_collection.find_one(
            {"_id": comment_oid, "likes": user_oid}
        )

        if comment:
            # User has liked, so unlike
            comments_collection.update_one(
                {"_id": comment_oid},
                {"$pull": {"likes": user_oid}}
            )
            return jsonify({"msg": "Comment unliked"}), 200
        else:
            # User has not liked, so like
            comments_collection.update_one(
                {"_id": comment_oid},
                {"$addToSet": {"likes": user_oid}}
            )
            return jsonify({"msg": "Comment liked"}), 200

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500


@comments_bp.route('/<comment_id>/dislike', methods=['POST'])
@jwt_required()
def dislike_comment(comment_id):
    """Toggles a dislike on a comment for the current user."""
    db = comments_bp.db
    comments_collection = db.comments
    current_user_id = get_jwt_identity()

    try:
        comment_oid = ObjectId(comment_id)
        user_oid = ObjectId(current_user_id)

        # Check if the user has already disliked the comment
        comment = comments_collection.find_one(
            {"_id": comment_oid, "dislikes": user_oid}
        )

        if comment:
            # User has disliked, so remove dislike
            comments_collection.update_one(
                {"_id": comment_oid},
                {"$pull": {"dislikes": user_oid}}
            )
            return jsonify({"msg": "Comment dislike removed"}), 200
        else:
            # User has not disliked, so add dislike
            comments_collection.update_one(
                {"_id": comment_oid},
                {"$addToSet": {"dislikes": user_oid}}
            )
            return jsonify({"msg": "Comment disliked"}), 200

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500