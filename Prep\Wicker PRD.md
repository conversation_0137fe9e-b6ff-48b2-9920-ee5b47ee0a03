# Product Requirements Document: Accra Local Discovery, Commerce & Delivery App
**Version:** 1.2
**Date:** 2025-05-08
**Status:** Draft

## 1. Introduction

### 1.1. Purpose
This document outlines the requirements for the Minimum Viable Product (MVP) and subsequent major feature enhancements (including a secure delivery platform) of a new mobile application focused on local discovery, community building, trusted peer-to-peer commerce, and secure item delivery within Accra, Ghana.

### 1.2. Product Vision & Goal
To become the most trusted and engaging platform in Accra for discovering local places and services, sharing experiences within a vibrant community, facilitating secure peer-to-peer transactions, and offering a privacy-centric, reliable delivery service.

The core goal is to solve the difficulty users face in finding reliable local businesses/services and getting items delivered securely, while empowering businesses/vendors with an easy-to-access online presence where reputation and privacy are paramount.

### 1.3. Target Audience
* **Primary (Discovery & Commerce):** Residents and visitors in Accra looking for places, services, recommendations, events. People seeking reliable handymen, artisans, local food vendors, etc.
* **Secondary (Discovery & Commerce):** Small and informal businesses/vendors in Accra seeking an online presence.
* **Primary (Delivery - Senders):** Individuals, e-commerce businesses, local retail shops in Accra needing reliable, trackable, and private delivery services.
* **Primary (Delivery - Recipients):** Individuals and businesses in Accra prioritizing privacy and secure receipt of items.
* **Primary (Delivery - Providers):** Individuals or small logistics companies in Accra seeking flexible earning opportunities for deliveries.

### 1.4. Key Differentiators
* **Trust & Verification (Discovery/Commerce):** Emphasis on verified information and robust review systems.
* **Integrated Secure Payments (Commerce & Delivery):** Future inclusion of escrow (MoMo) and secure payment for deliveries.
* **Community Focus:** Integrated discussion forums.
* **Streamlined Experience:** All-in-one platform.
* **Ease of Onboarding for Vendors/Service Providers.**
* **Enhanced Privacy for Deliveries:** User-generated encrypted keys for delivery addresses.

## 2. Guiding Principles
* Trust & Safety First.
* Community Driven.
* User-Centric Design (Hybrid UI: Brutalism/Glassmorphism).
* Mobile First (Flutter).
* Optimized for Accra Context (Connectivity, MoMo).
* Simplicity for Contributors/Vendors/Delivery Providers.
* **Privacy by Design (especially for Delivery).**

## 3. Proposed Features (MVP - Discovery & Basic Community)

This MVP focuses on establishing the core directory, community, and discovery features. E-commerce and Delivery functionalities are planned for post-MVP phases.

### 3.1. Core App Structure & Navigation
* Bottom Navigation Bar: Home, Explore, Contribute (+), Hub (Profile).
* Platform: Flutter, Flask Backend API. (Database: MongoDB).

### 3.2. User Authentication & Profiles
* Signup/Login (Email/Password, Phone, Social).
* Basic User Profile (Username, pic, bio, points).
    * *(Post-MVP: Profile will expand to accommodate roles like Sender, Recipient, Delivery Provider with specific attributes).*

### 3.3. Homepage (Personalized Feed)
* Vertical scroll feed, horizontal themed rows.
* Content: Trending, Based on Activity, Community Highlights, Popular Playlists, Newly Added.

### 3.4. Explore Page
* Themed content rows, search bar (Places, Services, Users, Communities, Playlists).

### 3.5. Place Directory & Details
* Place Detail View: Name, Category, Address (Map), Contact, Photos, Ratings/Reviews, Local Info, Community Threads.
* Map Integration (OpenStreetMap recommended).

### 3.6. Community Features (MVP)
* View Threads linked to places/topics.
* Read posts/replies.
* Create Threads, Reply, Upvote/Downvote
* Searchable comment section
* Grouped and filterable comment section based on sentiment analysis

### 3.7. Contributions
* Add New Place (Simple flow).
* Add Review/Rating.
* Add Photo.
* Create Playlist (Public/Private).
* Playlist Checklist (GPS Check-in).

### 3.8. Gamification (MVP)
* Point System for contributions.
* Display points/level on profile.

### 3.9. "Hub" Page (Profile Section - Name TBD)
* User Profile Management.
* My Contributions.
* My Playlists.


### 3.10. Verification & Trust (MVP Foundation)
* User Reporting/Flagging.

## 4. Post-MVP Features (Potential Phased Rollout)

### 4.1. E-commerce - Phase 1 (Listings & Basic Payments)
* User as Vendor: "Seller Mode".
* Inventory Listing.
* Cash on Delivery (COD) option.
* Transaction History in "Hub".
* Seller/Buyer Ratings for transactions.

### 4.2. E-commerce - Phase 2 (Secure Escrow Payments)
* Escrow Payment System (MoMo integration).
* Dispute resolution for escrow.

### 4.3. Verification & Trust - Phase 2
* Business Owner Claim Process.
* Owner Tools.

### 4.4. Community - Phase 2
* Creating new threads/communities.
* Advanced moderation.

### 4.5. Explore - Phase 2/3
* Advanced Swipe Mechanics (if validated).
* 3D Map Exploration.

### 4.6. E-commerce - Phase 3 (Advanced Marketplace)
* POS Functionality.
* Inventory Management tools.
* Promotions/Discount codes.
* **Reverse Marketplace (RFQ):** Customers post requests, vendors bid.

### **4.7. Secure Delivery Platform - Phase 4 (Major Feature Set)**

This phase integrates a secure delivery service, drawing heavily on the "SecureDispatch" concept.

* **4.7.1. Additional User Roles & Profiles:**
    * **Recipient (Delivery):** Manages encrypted delivery addresses, tracks incoming packages.
    * **Sender (Delivery):** Initiates deliveries, tracks outgoing packages, manages payments for delivery.
    * **Delivery Provider:** Accepts delivery jobs, navigates using temporarily decrypted addresses, manages earnings.
    * Profile sections within "Hub" will adapt to these roles, showing relevant dashboards and history.
* **4.7.2. Secure Address Management (Recipient Focused):**
    * **User-Generated Encrypted Keys:** Recipients generate strong symmetric keys (e.g., AES-256) client-side (Flutter app) for each saved delivery address.
    * **Secure Key Storage:** Keys stored exclusively on the Recipient's device (iOS Keychain/Android Keystore via `flutter_secure_storage`). **Platform backend never receives or stores these private keys.**
    * **Address Encryption:** Address details encrypted on Recipient's device before transmission. Only ciphertext and a non-sensitive address ID stored on the server.
    * **Key Backup/Recovery:** User responsibility with extensive platform guidance (e.g., export secure key file, mnemonic phrase). Explore opt-in, user-controlled secure recovery mechanisms.
    * **Address Input:** Manual, map pin, saved (encrypted) addresses. Consider What3Words integration.
* **4.7.3. Order Placement & Management (Sender Focused):**
    * **Create Delivery:** Senders input item details, pickup address (plaintext), and Recipient's authorized encrypted address token/ID.
    * **Select Delivery Options:** Standard/express, vehicle type (if applicable).
    * **View Estimated Cost & Pay:** Transparent pricing (see 4.7.8). Payment via integrated MoMo, Cards.
    * **Order Tracking:** Real-time status for Senders.
    * **Order Cancellation:** Conditional.
* **4.7.4. Delivery Acceptance & Management (Delivery Provider Focused):**
    * **Verification:** Stringent verification for Delivery Providers (ID, vehicle docs, license).
    * **View & Accept Jobs:** Matched based on location/status.
    * **Secure Temporary Address Access:**
        * Assigned Delivery Provider gets temporary, secure access to the decrypted Recipient's address *only for the active delivery*.
        * Mechanism: Explore Proxy Re-Encryption (PRE) or Time-Limited Decryption Tokens. Recipient's private key is never shared.
        * Decrypted address visible only within the app, copy-paste disabled, cleared after delivery.
    * **Status Updates:** "Picked Up," "En Route," "Arrived," "Delivered," "Issue Reported."
    * **Proof of Delivery:** Photo, signature, or Recipient confirmation code.
    * **Earnings Dashboard:** Track earnings, manage payouts (MoMo, bank).
* **4.7.5. Real-Time Tracking (Sender & Recipient):**
    * Live map view of Delivery Provider.
    * Dynamic ETA updates.
* **4.7.6. Communication (Delivery Focused):**
    * Secure, E2EE in-app chat: Sender-Provider, Recipient-Provider (for active orders).
    * User-Platform Support chat.
* **4.7.7. Payment Integration (Delivery Focused):**
    * Mandatory MoMo (MTN, Vodafone Cash, AirtelTigo Money) for Senders paying and Provider payouts.
    * Card payments.
    * Secure Payment Gateway.
* **4.7.8. Pricing & Monetization (Delivery Focused):**
    * **Flexible Pricing Model:** Base fare, distance fee, time fee, item characteristics (weight/size tiers, optional insurance), service tiers (standard/express), surge pricing. Transparency is key.
    * **Platform Commission:** Percentage from total delivery fee (e.g., 15-25%).
    * **Potential Other Revenue:** Subscription plans for high-volume Senders, value-added services (enhanced insurance).
* **4.7.9. Rating & Review System (Delivery Focused):**
    * Senders rate Delivery Providers & experience.
    * Recipients rate Delivery Providers & experience.
    * Providers may rate Senders (package readiness, etc.).
* **4.7.10. Notifications (Delivery Focused):**
    * Push notifications for Senders, Recipients, Providers on order status, job availability, payments, etc.
* **4.7.11. Admin Panel Enhancements (Delivery Focused):**
    * Management of Delivery Providers (verification, status).
    * Monitoring delivery operations.
    * Delivery-specific dispute resolution.
    * Configuration of delivery pricing parameters.

## 5. Non-Functional Requirements

* Platform: Flutter, Flask Backend API. (Database: To be finalized - consider PostgreSQL or MongoDB. MongoDB offers good geospatial query support beneficial for delivery logistics).
* UI Style: Hybrid (Brutalism/Glassmorphism).
* Performance: Responsive app, optimized encryption/decryption, fast API.
* Security: Secure authentication, API protection, secure data handling. **For Delivery: Emphasis on secure key management, temporary address decryption, E2EE chat, compliance with Ghana Data Protection Act.**
* Scalability: Handle growing users, transactions, deliveries.
* Reliability & Availability: High uptime, fault tolerance, data backup (excluding user-private address keys).
* Usability: Intuitive UI. **Crucially for Delivery: Simple and clear UX for encrypted address and key management.**
* Maintainability: Well-structured, documented code.
* Localization: Initial English, design for future languages.

## 6. Open Questions / Future Considerations

* Finalize "Hub" name.
* MVP recommendation engine logic.
* Gamification point values/levels.
* Long-term monetization (overall app).
* Dispute resolution for escrow.
* GPS check-in radius/logic.
* **Database Choice:** Finalize between SQL (e.g., PostgreSQL) and NoSQL (e.g., MongoDB), considering the complex needs of discovery, e-commerce, and the geospatial/flexible data nature of the delivery platform.
* **Delivery Key Management UX:** Extensive research and testing for user-managed key backup/recovery to minimize friction.
* **Delivery Temporary Address Access Mechanism:** Detailed technical evaluation of PRE vs. token-based decryption for riders.

## 7. Release Criteria (MVP - Discovery & Basic Community)
* All features in Section 3 implemented.
* Secure and functional User Authentication.
* Core flows stable and intuitive.
* Basic reporting/flagging functional.
* Acceptable performance on target devices.
* No critical bugs/vulnerabilities.
* Basic analytics integrated.

