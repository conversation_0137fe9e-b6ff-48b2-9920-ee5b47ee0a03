import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:wicker/screens/add_place_screen.dart';
import 'package:wicker/screens/create_playlist_screen.dart';
import 'package:wicker/screens/create_post_screen.dart';
import 'package:wicker/screens/create_queue_screen.dart';
import 'package:wicker/screens/explore_screen.dart';
import 'package:wicker/screens/home_screen.dart';
import 'package:wicker/screens/hub_screen.dart';
import 'package:wicker/screens/login_screen.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/widgets/neu_bottom_nav_bar.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

void main() {
  runApp(const WickerApp());
}

class WickerApp extends StatelessWidget {
  const WickerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wicker App',
      theme: ThemeData(
        primarySwatch: Colors.teal, // A slightly different theme color
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      debugShowCheckedModeBanner: false,
      home: const AuthWrapper(), // Start with the AuthWrapper
    );
  }
}

// This widget will decide which screen to show
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  _AuthWrapperState createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final AuthService _authService = AuthService();

  // This future now handles checking and refreshing the token
  Future<bool> _checkLoginStatus() async {
    final accessToken = await _authService.getAccessToken();
    if (accessToken == null) {
      return false; // Not logged in
    }

    // Check if the access token is expired or nearing expiration (e.g., within 5 minutes)
    if (JwtDecoder.isExpired(accessToken) ||
        JwtDecoder.getRemainingTime(accessToken).inMinutes < 5) {
      print(
        "Access token expired or nearing expiration, attempting refresh...",
      );
      return await _authService.refreshToken();
    }

    return true; // Token is valid
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: _checkLoginStatus(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        // If checkLoginStatus returned true, user is logged in
        if (snapshot.data == true) {
          return const MainScreen();
        }

        // Otherwise, show the login screen
        return const LoginScreen();
      },
    );
  }
}

class MainScreen extends StatefulWidget {
  final int initialIndex;
  const MainScreen({super.key, this.initialIndex = 0}); // Add initialIndex

  @override
  _MainScreenState createState() => _MainScreenState();
}

// In lib/main.dart

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
  }

  final List<Widget> _screens = [
    HomeScreen(onSearchTap: () {}),
    const ExploreScreen(),
    const HubScreen(),
  ];

  /// A new helper widget to build the styled buttons for the contribute modal.
  Widget _buildContributeButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: NeuCard(
        margin: const EdgeInsets.all(8),
        padding: const EdgeInsets.symmetric(vertical: 24),
        backgroundColor: color,
        shadowOffset: 6,
        borderWidth: 3,
        child: SizedBox(
          width: 120, // Give the buttons a consistent width
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 48, color: Colors.white),
              const SizedBox(height: 12),
              Text(
                label,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// The redesigned bottom sheet using the new helper widget.
  void _showContributeBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFFFEF7F0), // Light beige background
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        side: BorderSide(color: Colors.black, width: 3),
      ),
      builder: (context) {
        // A helper function to handle navigation after a button is tapped.
        void navigate(Widget screen) {
          Navigator.pop(context); // Close the modal first
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => screen),
          );
        }

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 32.0, horizontal: 16.0),
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: 16,
            runSpacing: 16,
            children: [
              _buildContributeButton(
                icon: EvaIcons.pinOutline,
                label: 'Add Place',
                color: const Color(0xFF4ECDC4), // Teal
                onPressed: () => navigate(const AddPlaceScreen()),
              ),
              _buildContributeButton(
                icon: EvaIcons.messageSquareOutline,
                label: 'Create Post',
                color: const Color(0xFFFF6B6B), // Coral
                onPressed: () => navigate(const CreatePostScreen()),
              ),
              _buildContributeButton(
                icon: EvaIcons.bookmarkOutline,
                label: 'New Queue',
                color: const Color(0xFF6C5CE7), // Vibrant Blue
                onPressed: () => navigate(const CreateQueueScreen()),
              ),
              _buildContributeButton(
                icon: EvaIcons.browserOutline,
                label: 'New Playlist',
                color: const Color(0xFFFFE66D), // Yellow
                onPressed: () => navigate(const CreatePlaylistScreen()),
              ),
            ],
          ),
        );
      },
    );
  }

  void onTabTapped(int index) {
    if (index == 1) {
      _showContributeBottomSheet();
      return;
    }
    int screenIndex = index > 1 ? index - 1 : index;
    setState(() {
      _currentIndex = screenIndex;
    });
  }

  @override
  Widget build(BuildContext context) {
    int navBarIndex = _currentIndex > 0 ? _currentIndex + 1 : _currentIndex;
    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: _screens),
      bottomNavigationBar: NeuBottomNavBar(
        currentIndex: navBarIndex,
        onTabTapped: onTabTapped,
        items: [
          NeuNavBarItem(
            icon: EvaIcons.homeOutline,
            activeIcon: EvaIcons.home,
            label: 'Home',
          ),
          NeuNavBarItem(
            icon: EvaIcons.plusCircleOutline,
            label: 'Contribute',
            iconSize: 32,
          ),
          NeuNavBarItem(
            icon: EvaIcons.searchOutline,
            activeIcon: EvaIcons.search,
            label: 'Explore',
          ),
          NeuNavBarItem(
            icon: EvaIcons.personOutline,
            activeIcon: EvaIcons.person,
            label: 'Hub',
          ),
        ],
      ),
    );
  }
}

//
