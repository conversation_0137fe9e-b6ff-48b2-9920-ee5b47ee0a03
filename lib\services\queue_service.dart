import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:wicker/services/places_service.dart'; // We'll use the WickerHttpClient
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/auth_service.dart';

class QueueService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;
  final AuthService _authService = AuthService();

  Future<String> createQueue({
    required String name,
    required bool isPrivate,
  }) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.post(
        Uri.parse('$baseUrl/api/queues/create'),
        body: jsonEncode({'name': name, 'is_private': isPrivate}),
      );

      final responseBody = jsonDecode(response.body);
      if (response.statusCode == 201) {
        return responseBody['msg'];
      } else {
        throw Exception(responseBody['msg'] ?? 'Failed to create queue');
      }
    } catch (e) {
      rethrow;
    }
  }

  // Method to fetch the user's queues
  Future<List<Map<String, dynamic>>> getMyQueues() async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.get(Uri.parse('$baseUrl/api/queues/'));
      if (response.statusCode == 200) {
        List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load queues');
      }
    } catch (e) {
      rethrow;
    }
  }

  // In lib/services/queue_service.dart

  Future<void> addItemToQueue({
    required String queueId,
    required String itemId,
    required String itemType,
  }) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.post(
        Uri.parse('$baseUrl/api/queues/$queueId/add-item'),
        body: jsonEncode({'item_id': itemId, 'item_type': itemType}),
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to add item to queue');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getQueueDetails(String queueId) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.get(
        Uri.parse('$baseUrl/api/queues/$queueId'),
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load queue details');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> planQueue({
    required String queueId,
    required DateTime startDateTime, // 1. Add the new parameter
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _authService.getAccessToken();
    if (token == null) throw Exception('Authentication Token not found.');

    final response = await http.post(
      Uri.parse('$baseUrl/api/queues/$queueId/plan'),
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json; charset=UTF-8',
      },
      // 2. Add the startDateTime to the request body
      body: jsonEncode({'start_datetime': startDateTime.toIso8601String()}),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      final responseBody = jsonDecode(response.body);
      throw Exception(responseBody['msg'] ?? 'Failed to plan queue');
    }
  }

  // In lib/services/queue_service.dart

  Future<void> removeItemFromQueue(String queueId, String itemId) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      final response = await _client.delete(
        Uri.parse('$baseUrl/api/queues/$queueId/items/$itemId'),
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to remove item from queue');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> deleteQueue(String queueId) async {
    try {
      final baseUrl = await _config.getBaseUrl();
      // Use POST as defined in our updated backend route
      final response = await _client.post(
        Uri.parse('$baseUrl/api/queues/$queueId/delete'),
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to delete queue');
      }
    } catch (e) {
      rethrow;
    }
  }
}
