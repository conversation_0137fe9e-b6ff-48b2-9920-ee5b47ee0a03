import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/services/user_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/services/config_service.dart';

class EditProfileScreen extends StatefulWidget {
  final Map<String, dynamic> profileData;
  const EditProfileScreen({super.key, required this.profileData});

  @override
  _EditProfileScreenState createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  late TextEditingController _usernameController;
  late TextEditingController _bioController;
  final UserService _userService = UserService();
  final ImagePicker _picker = ImagePicker();
  XFile? _newAvatar;
  bool _isLoading = false;

  // --- THE FIX: State variable and service instance ---
  final ConfigService _configService = ConfigService.instance;
  String _baseUrl = '';
  // --- End of FIX ---

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController(
      text: widget.profileData['username'],
    );
    _bioController = TextEditingController(text: widget.profileData['bio']);
    _loadConfig(); // Load the server configuration
  }

  // --- THE FIX: New method to load the base URL ---
  Future<void> _loadConfig() async {
    final url = await _configService.getBaseUrl();
    if (mounted) {
      setState(() {
        _baseUrl = url;
      });
    }
  }
  // --- End of FIX ---

  Future<void> _pickImage() async {
    final XFile? pickedFile = await _picker.pickImage(
      source: ImageSource.gallery,
    );
    if (pickedFile != null) {
      setState(() => _newAvatar = pickedFile);
    }
  }

  void _submit() async {
    setState(() => _isLoading = true);
    try {
      await _userService.updateProfile(
        username: _usernameController.text,
        bio: _bioController.text,
      );
      if (_newAvatar != null) {
        await _userService.updateAvatar(_newAvatar!);
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile updated successfully!')),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Construct the full profile picture URL
    String profilePicPath =
        widget.profileData['profile_pic_url']?.toString() ?? '';
    String profilePicUrl = '';

    if (profilePicPath.isNotEmpty && _baseUrl.isNotEmpty) {
      // Ensure the path uses forward slashes
      profilePicUrl = '$_baseUrl/${profilePicPath.replaceAll('\\', '/')}';
    } else if (profilePicPath.isEmpty) {
      // Fallback to a placeholder if no path is provided
      profilePicUrl =
          'https://i.pravatar.cc/150?u=${widget.profileData['_id']?['\$oid']}';
    }

    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: const Text(
            'Edit Profile',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Center(
              child: Stack(
                children: [
                  NeuCard(
                    padding: const EdgeInsets.all(4),
                    child: CircleAvatar(
                      radius: 60,
                      backgroundImage:
                          _baseUrl.isEmpty && profilePicPath.isNotEmpty
                          ? null
                          : (_newAvatar != null
                                ? FileImage(File(_newAvatar!.path))
                                : profilePicUrl.isNotEmpty
                                ? NetworkImage(profilePicUrl)
                                : null),
                      child: _baseUrl.isEmpty && profilePicPath.isNotEmpty
                          ? const CircularProgressIndicator()
                          : null,
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: _pickImage,
                      child: const NeuCard(
                        padding: EdgeInsets.all(8),
                        child: Icon(Icons.camera_alt, size: 20),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Username',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            NeuCard(
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                controller: _usernameController,
                decoration: const InputDecoration(border: InputBorder.none),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Bio',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            NeuCard(
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: TextField(
                controller: _bioController,
                maxLength: 150,
                maxLines: 4,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  counterText: "",
                ),
              ),
            ),
            const SizedBox(height: 40),
            GestureDetector(
              onTap: _isLoading ? null : _submit,
              child: NeuCard(
                margin: EdgeInsets.zero,
                padding: const EdgeInsets.all(16),
                backgroundColor: const Color(0xFF00D2D3), // Cyan
                child: Center(
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 3,
                            color: Colors.white,
                          ),
                        )
                      : const Text(
                          'Save Changes',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
