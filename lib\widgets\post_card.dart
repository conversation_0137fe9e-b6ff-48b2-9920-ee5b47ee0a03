import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:wicker/screens/hub_screen.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/post_service.dart';
import 'package:wicker/services/share_service.dart';
import 'package:wicker/services/time_ago_service.dart';
import 'package:wicker/widgets/add_to_playlist_modal.dart';
import 'package:wicker/widgets/comments_modal.dart';
import 'package:wicker/widgets/media_player.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/report_modal.dart';

class PostCard extends StatefulWidget {
  final Map<String, dynamic> postData;
  const PostCard({super.key, required this.postData});

  @override
  _PostCardState createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  // --- State for UI interaction ---
  late int likeCount;
  late int dislikeCount;
  late int commentCount;
  late bool isLiked;
  late bool isDisliked;
  int _currentPage = 0;

  // --- State for processed data to display ---
  String _title = '';
  String _posterName = 'Unknown';
  String _avatarUrl = '';
  String _postedTime = '';
  List<dynamic> _mediaList = [];
  String _baseUrl = '';
  bool _isDataProcessed = false;

  final PostService _postService = PostService();
  final ConfigService _configService = ConfigService.instance;
   final ShareService _shareService = ShareService(); // Add ShareService

  @override
  void initState() {
    super.initState();
    _initializeCard();
  }

  /// Initializes all card data, including async calls.
  Future<void> _initializeCard() async {
    await _processDataForDisplay();
    _updateInteractionState();
  }

  /// The single source of truth for processing raw post or place data into a
  /// display-ready format. Now simplified thanks to consistent backend data.
  Future<void> _processDataForDisplay() async {
    final item = widget.postData;
    final contentType = item['contribution_type'] ?? 'post';
    final tempBaseUrl = await _configService.getBaseUrl();

    // 1. Process Title
    _title = item['text_content'] ?? item['name'] ?? 'Untitled';

    // 2. Process Author Details (Now always present)
    final authorDetails = item['author_details'] as Map<String, dynamic>? ?? {};
    _posterName = authorDetails['username'] ?? 'Unknown';

    // 3. Process Avatar URL
    String? picPath = authorDetails['profile_pic_url']?.toString();
    if (picPath != null && picPath.isNotEmpty) {
      _avatarUrl = '$tempBaseUrl/${picPath.replaceAll('\\', '/')}';
    } else {
      _avatarUrl =
          'https://i.pravatar.cc/150?u=${authorDetails['_id']?['\$oid']}';
    }

    // 4. Process Media List
    if (contentType == 'place') {
      final List<dynamic> photoList = item['photos'] as List<dynamic>? ?? [];
      _mediaList = photoList
          .map((path) => {'path': path, 'type': 'image', 'aspect_ratio': 1.0})
          .toList();
    } else {
      _mediaList = item['media'] as List<dynamic>? ?? [];
    }

    // 5. Process Timestamp
    final createdAt = item['created_at']?['\$date']?.toString();
    _postedTime = TimeAgoService.format(createdAt);

    if (mounted) {
      setState(() {
        _baseUrl = tempBaseUrl;
        _isDataProcessed = true;
      });
    }
  }

  void _updateInteractionState() {
    likeCount = (widget.postData['likes'] as List?)?.length ?? 0;
    dislikeCount = (widget.postData['dislikes'] as List?)?.length ?? 0;
    commentCount = widget.postData['comment_count'] as int? ?? 0;
    isLiked = widget.postData['isLiked'] as bool? ?? false;
    isDisliked = widget.postData['isDisliked'] as bool? ?? false;
  }
  // --- ACTION BUTTON HANDLERS ---

  void _onLike() {
    final postId = widget.postData['_id']['\$oid'];
    setState(() {
      if (isLiked) {
        isLiked = false;
        likeCount--;
      } else {
        isLiked = true;
        likeCount++;
        if (isDisliked) {
          isDisliked = false;
          dislikeCount--;
        }
      }
    });
    _postService.likePost(postId);
  }

  void _onDislike() {
    final postId = widget.postData['_id']['\$oid'];
    setState(() {
      if (isDisliked) {
        isDisliked = false;
        dislikeCount--;
      } else {
        isDisliked = true;
        dislikeCount++;
        if (isLiked) {
          isLiked = false;
          likeCount--;
        }
      }
    });
    _postService.dislikePost(postId);
  }

  void _onComment() async {
    final newCommentPosted = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FractionallySizedBox(
        heightFactor: 0.9,
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: CommentsModal(postId: widget.postData['_id']['\$oid']),
        ),
      ),
    );

    if (newCommentPosted == true && mounted) {
      setState(() {
        commentCount++;
      });
    }
  }

  void _onSave() {
    showModalBottomSheet(
      context: context,
      builder: (context) => AddToPlaylistModal(
        itemId: widget.postData['_id']['\$oid'],
        itemType: 'post',
      ),
    );
  }

  void _onShare() {
    String shareText = 'Check out this post from Wicker!';
    if (_title.isNotEmpty) {
      shareText = 'Check out this post on Wicker: $_title';
    }
    _shareService.shareContent(shareText);
  }

  
  void _navigateToHub() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const HubScreen()),
    );
  }





  @override
  Widget build(BuildContext context) {
    if (!_isDataProcessed) {
      return const SizedBox(
        height: 300,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: EdgeInsets.zero,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(13),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            GestureDetector(
              onTap: _navigateToHub,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(12, 12, 12, 8),
                child: Row(
                  children: [
                    NeuCard(
                      padding: const EdgeInsets.all(2),
                      margin: EdgeInsets.zero,
                      borderWidth: 2,
                      shadowOffset: 3,
                      child: CircleAvatar(
                        radius: 20,
                        backgroundImage: NetworkImage(_avatarUrl),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _posterName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            _postedTime,
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  PopupMenuButton<String>(
                      icon: const Icon(EvaIcons.moreVerticalOutline),
                      onSelected: (value) {
                        if (value == 'report') {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            builder: (context) => ReportModal(
                              itemId: widget.postData['_id']['\$oid'],
                              itemType: 'post',
                            ),
                          );
                        } else if (value == 'share') {
                          _onShare();
                        }
                      },
                      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                        const PopupMenuItem<String>(
                          value: 'share',
                          child: ListTile(leading: Icon(EvaIcons.shareOutline), title: Text('Share')),
                        ),
                        const PopupMenuItem<String>(
                          value: 'report',
                          child: ListTile(
                            leading: Icon(EvaIcons.flagOutline, color: Colors.red),
                            title: Text('Report', style: TextStyle(color: Colors.red)),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
                  ],
                ),
              ),
              );
            
            // Content Section
    //         if (_title.isNotEmpty)
    //           Padding(
    //             padding: const EdgeInsets.symmetric(
    //               horizontal: 16,
    //               vertical: 8,
    //             ),
    //             child: Text(_title, style: const TextStyle(fontSize: 16)),
    //           ),
    //         if (_mediaList.isNotEmpty && _baseUrl.isNotEmpty)
    //           GestureDetector(
    //             onDoubleTap: _onLike,
    //             child: SizedBox(
    //               height: 300,
    //               child: PageView.builder(
    //                 itemCount: _mediaList.length,
    //                 onPageChanged: (index) =>
    //                     setState(() => _currentPage = index),
    //                 itemBuilder: (context, index) {
    //                   final mediaItem =
    //                       _mediaList[index] as Map<String, dynamic>;
    //                   return MediaPlayer(
    //                     mediaData: mediaItem,
    //                     baseUrl: _baseUrl,
    //                   );
    //                 },
    //               ),
    //             ),
    //           ),
    //         if (_mediaList.length > 1)
    //           _buildCarouselIndicator(_mediaList.length),
    //         // Action Buttons Section
    //         Padding(
    //           padding: const EdgeInsets.all(8.0),
    //           child: Row(
    //             mainAxisAlignment: MainAxisAlignment.spaceAround,
    //             children: [
    //               // Like, Dislike, Comment, and Save buttons...                  
    //               _buildActionButton(
    //                 icon: isLiked ? EvaIcons.heart : EvaIcons.heartOutline,
    //                 label: '$likeCount',
    //                 onPressed: _onLike,
    //                 color: isLiked ? Colors.red : null,
    //               ),
    //               _buildActionButton(
    //                 icon: isDisliked ? EvaIcons.slash : EvaIcons.slashOutline,
    //                 label: '$dislikeCount',
    //                 onPressed: _onDislike,
    //                 color: isDisliked ? Colors.blueGrey : null,
    //               ),
    //               _buildActionButton(
    //                 icon: EvaIcons.messageSquareOutline,
    //                 label: '$commentCount',
    //                 onPressed: () async {
    //                   final didPostComment = await showModalBottomSheet<bool>(
    //                     context: context,
    //                     isScrollControlled: true,
    //                     builder: (context) => CommentsModal(
    //                       postId: widget.postData['_id']['\$oid'],
    //                     ),
    //                   );
    //                   if (didPostComment == true) {
    //                     // Refresh comments if a new one was posted
    //                     // For now, just re-fetch the post to update comment count
    //                     final updatedPost = await _postService
    //                         .getPostById(widget.postData['_id']['\$oid']);
    //                     if (updatedPost != null && mounted) {
    //                       setState(() {
    //                         widget.postData['comment_count'] =
    //                             updatedPost['comment_count'];
    //                         _updateInteractionState(); // Re-read counts from updated data
    //                       });
    //                     }
    //                   }
    //                 },
    //               ),
    //               _buildActionButton(
    //                 icon: EvaIcons.bookmarkOutline,
    //                 label: 'Save',
    //                 onPressed: () {
    //                   // TODO: Implement save functionality
    //                 },
    //               ),
                  
    //             ],
    //           ),
    //         ),
    //       ],
    //     ),
    //   ),
    // );
                Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildActionButton(
                    icon: isLiked ? EvaIcons.arrowUpward : EvaIcons.arrowUpwardOutline,
                    label: likeCount.toString(),
                    color: isLiked ? Colors.white : Colors.black,
                    backgroundColor: isLiked ? const Color(0xFF4ECDC4) : Colors.white,
                    onPressed: _onLike,
                  ),
                  _buildActionButton(
                    icon: isDisliked ? EvaIcons.arrowDownward : EvaIcons.arrowDownwardOutline,
                    label: dislikeCount.toString(),
                    color: isDisliked ? Colors.white : Colors.black,
                    backgroundColor: isDisliked ? const Color(0xFFFF6B6B) : Colors.white,
                    onPressed: _onDislike,
                  ),
                  _buildActionButton(
                    icon: EvaIcons.messageSquareOutline,
                    label: commentCount.toString(),
                    onPressed: _onComment,
                  ),
                  _buildActionButton(
                    icon: EvaIcons.bookmarkOutline,
                    label: 'Save',
                    onPressed: _onSave,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
    Color? backgroundColor,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onPressed,
        child: NeuCard(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.symmetric(vertical: 8),
          backgroundColor: backgroundColor ?? Colors.white,
          shadowOffset: 4,
          borderWidth: 2,
          borderRadius: 12,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color ?? Colors.black, size: 20),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  color: color ?? Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCarouselIndicator(int itemCount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(itemCount, (index) {
        return Container(
          width: 8.0,
          height: 8.0,
          margin: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 2.0),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentPage == index
                ? Colors.teal
                : Colors.grey.withOpacity(0.5),
          ),
        );
      }),
    );
  }
}
