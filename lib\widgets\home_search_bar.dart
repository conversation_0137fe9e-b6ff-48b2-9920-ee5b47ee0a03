import 'package:flutter/material.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

class HomeSearchBar extends StatelessWidget {
  final VoidCallback onTap;

  const HomeSearchBar({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: NeuCard(
        margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        backgroundColor: Colors.white,
        borderRadius: 30.0, // More rounded for a search bar
        shadowOffset: 4.0,
        borderWidth: 2.0,
        child: const Row(
          children: [
            Icon(EvaIcons.search, color: Colors.grey),
            SizedBox(width: 10),
            Text(
              'Search places, services, items...',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
