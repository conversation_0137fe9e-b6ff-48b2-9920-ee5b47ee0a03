import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import '../widgets/custom_map_marker.dart';
import '../services/places_service.dart';
import '../main.dart';
import 'package:image_picker/image_picker.dart';

class AddPlaceScreen extends StatefulWidget {
  const AddPlaceScreen({super.key});

  @override
  State<AddPlaceScreen> createState() => _AddPlaceScreenState();
}

class _AddPlaceScreenState extends State<AddPlaceScreen> {
  final MapController _mapController = MapController();
  final PlacesService _placesService = PlacesService();

  LatLng _currentMapCenter = const LatLng(5.6037, -0.1870); // Default: Accra
  String? _placeName;
  String? _category;
  double? _rating;
  String? _review;
  bool _isLoading = false;

  final List<Map<String, dynamic>> _categories = [
    {
      'name': 'Restaurant',
      'icon': Icons.restaurant_menu,
      'color': const Color(0xFF4ECDC4),
    },
    {
      'name': 'Cafe',
      'icon': Icons.local_cafe,
      'color': const Color(0xFF6C5CE7),
    },
    {'name': 'Shop', 'icon': Icons.store, 'color': const Color(0xFF00D2D3)},
    {
      'name': 'Art Gallery',
      'icon': Icons.palette,
      'color': const Color(0xFFFF6B6B),
    },
    {'name': 'Other', 'icon': EvaIcons.pin, 'color': Colors.grey},
  ];

  final List<XFile> _selectedImages = [];
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _centerMapOnUserLocation();
  }

  Future<void> _centerMapOnUserLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      setState(() {
        _currentMapCenter = LatLng(position.latitude, position.longitude);
        _mapController.move(_currentMapCenter, 14.0);
      });
    } catch (e) {
      debugPrint("Could not get location: $e");
    }
  }

  // --- DIALOG AND BOTTOM SHEET FUNCTIONS ---

  /// Shows a styled dialog for entering the place name.
  void _showNameInputDialog() {
    final nameController = TextEditingController(text: _placeName);
    _showNeuDialog(
      title: 'Name of Place',
      content: NeuCard(
        margin: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        backgroundColor: Colors.grey.shade100,
        child: TextField(
          controller: nameController,
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'e.g., Osu Night Market',
            border: InputBorder.none,
          ),
        ),
      ),
      onConfirm: () {
        if (nameController.text.isNotEmpty) {
          setState(() => _placeName = nameController.text);
        }
        Navigator.pop(context);
      },
    );
  }

  /// Shows a styled dialog for adding a star rating.
  void _showRatingDialog() {
    double tempRating = _rating ?? 0;
    _showNeuDialog(
      title: 'Add a Rating',
      content: Center(
        child: RatingBar.builder(
          initialRating: tempRating,
          minRating: 1,
          allowHalfRating: true,
          itemBuilder: (context, _) =>
              const Icon(Icons.star, color: Color(0xFFFFE66D)),
          onRatingUpdate: (newRating) {
            tempRating = newRating;
          },
        ),
      ),
      onConfirm: () {
        setState(() => _rating = tempRating);
        Navigator.pop(context);
      },
    );
  }

  /// Shows a styled bottom sheet for selecting a category.
  void _showCategoryPickerDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFFFEF7F0), // Light beige background
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        side: BorderSide(color: Colors.black, width: 3),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select a Category',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ..._categories.map((category) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: GestureDetector(
                    onTap: () {
                      setState(() => _category = category['name']);
                      Navigator.pop(context);
                    },
                    child: NeuCard(
                      margin: EdgeInsets.zero,
                      backgroundColor: category['color'],
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(category['icon'], color: Colors.white),
                          const SizedBox(width: 12),
                          Text(
                            category['name'],
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }

  /// Shows a styled dialog for writing a review.
  void _showReviewInputDialog() {
    final reviewController = TextEditingController(text: _review);
    _showNeuDialog(
      title: 'Add a Review',
      content: NeuCard(
        margin: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        backgroundColor: Colors.grey.shade100,
        child: TextField(
          controller: reviewController,
          autofocus: true,
          maxLines: 5,
          decoration: const InputDecoration(
            hintText: 'Share your experience...',
            border: InputBorder.none,
          ),
        ),
      ),
      onConfirm: () {
        if (reviewController.text.isNotEmpty) {
          setState(() => _review = reviewController.text);
        }
        Navigator.pop(context);
      },
    );
  }

  // --- NEW HELPER WIDGETS FOR DIALOGS ---

  /// A generic helper to show a styled Neubrutalism dialog.
  Future<void> _showNeuDialog({
    required String title,
    required Widget content,
    required VoidCallback onConfirm,
  }) {
    return showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: NeuCard(
          backgroundColor: const Color(0xFFFEF7F0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              content,
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  _buildDialogActionButton(
                    label: 'Cancel',
                    onTap: () => Navigator.pop(context),
                  ),
                  const SizedBox(width: 8),
                  _buildDialogActionButton(
                    label: 'Save',
                    onTap: onConfirm,
                    backgroundColor: const Color(0xFF00D2D3), // Cyan
                    textColor: Colors.white,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// A helper to build styled action buttons for the dialog.
  Widget _buildDialogActionButton({
    required String label,
    required VoidCallback onTap,
    Color? backgroundColor,
    Color? textColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: NeuCard(
        margin: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        backgroundColor: backgroundColor ?? Colors.white,
        shadowOffset: 4,
        borderWidth: 2,
        child: Text(
          label,
          style: TextStyle(
            color: textColor ?? Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// Submits the place data to the backend
  Future<void> _submitPlace() async {
    setState(() => _isLoading = true);
    try {
      if (_placeName == null || _placeName!.isEmpty) {
        throw Exception('Please enter a name for the place');
      }
      if (_category == null) {
        throw Exception('Please select a category for the place');
      }

      await _placesService.addPlace(
        name: _placeName!,
        category: _category!,
        latitude: _currentMapCenter.latitude,
        longitude: _currentMapCenter.longitude,
        images: _selectedImages,
        rating: _rating,
        review: _review,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Place added successfully!')),
        );
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const MainScreen()),
          (_) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  // --- NEW: Function to get user location and center the map ---
  Future<void> _centerOnUserLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Handle case where location services are disabled
      return;
    }
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Handle case where permission is denied
        return;
      }
    }
    Position position = await Geolocator.getCurrentPosition();
    _mapController.move(LatLng(position.latitude, position.longitude), 14.0);
  }

  // NEW: Uses the image_picker to get an image
  Future<void> _pickImage(ImageSource source) async {
    final XFile? pickedFile = await _picker.pickImage(source: source);
    if (pickedFile != null) {
      setState(() {
        _selectedImages.add(pickedFile);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0), // Light beige background
      body: Stack(
        children: [
          // The Map
          FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: _currentMapCenter,
              initialZoom: 14.0,
              onPositionChanged: (position, hasGesture) {
                if (hasGesture) {
                  setState(() => _currentMapCenter = position.center);
                }
              },
            ),
            children: [
              TileLayer(
                // Using CartoDB Positron for a clean, colorful aesthetic
                // Alternative options:
                // Watercolor style: 'https://stamen-tiles.a.ssl.fastly.net/watercolor/{z}/{x}/{y}.jpg'
                // Dark mode: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png'
                // Voyager (colorful): 'https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png'
                urlTemplate:
                    'https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png',
                subdomains: const ['a', 'b', 'c', 'd'],
                additionalOptions: const {
                  'attribution': '© OpenStreetMap contributors, © CARTO',
                },
              ),
            ],
          ),
          //
          // --- THE FIX: "Center on Me" button is now outside the map's ClipRRect ---
          Positioned(
            bottom: _placeName == null
                ? 32
                : 220, // Adjust position when card is shown
            right: 16,
            child: GestureDetector(
              onTap: _centerOnUserLocation,
              child: const NeuCard(
                padding: EdgeInsets.all(12),
                child: Icon(Icons.my_location),
              ),
            ),
          ),
          // Center Marker
          Center(
            child: CustomMapMarker(
              placeName: _placeName,
              category: _category,
              rating: _rating,
            ),
          ),
          _buildTopButtons(),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [_buildImagePreviews(), _buildActionToolbar()],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionToolbar() {
    return NeuCard(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(vertical: 8),
      backgroundColor: const Color(0xFF6C5CE7), // Vibrant Blue
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildToolbarButton(
            icon: EvaIcons.text,
            label: 'Name',
            onTap: _showNameInputDialog,
          ),
          _buildToolbarButton(
            icon: EvaIcons.gridOutline,
            label: 'Category',
            onTap: _showCategoryPickerDialog,
          ),
          _buildToolbarButton(
            icon: EvaIcons.messageSquareOutline,
            label: 'Review',
            onTap: _showReviewInputDialog,
          ),
          _buildToolbarButton(
            icon: EvaIcons.starOutline,
            label: 'Rating',
            onTap: _showRatingDialog,
          ),
          _buildToolbarButton(
            icon: EvaIcons.cameraOutline,
            label: 'Photo',
            onTap: () => _pickImage(ImageSource.gallery),
          ),
        ],
      ),
    );
  }

  /// Helper to build a single button for the action toolbar.
  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, color: Colors.white, size: 28),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the horizontal list of image previews.
  Widget _buildImagePreviews() {
    if (_selectedImages.isEmpty) {
      return const SizedBox.shrink();
    }
    return NeuCard(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
      padding: const EdgeInsets.all(8),
      backgroundColor: Colors.white.withOpacity(0.9),
      child: SizedBox(
        height: 80,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: _selectedImages.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                borderWidth: 2.0,
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(13),
                      child: kIsWeb
                          ? FutureBuilder<Uint8List>(
                              future: _selectedImages[index].readAsBytes(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return Image.memory(
                                    snapshot.data!,
                                    fit: BoxFit.cover,
                                    width: 80,
                                    height: 80,
                                  );
                                }
                                return const SizedBox(
                                  width: 80,
                                  height: 80,
                                  child: Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                );
                              },
                            )
                          : Image.file(
                              File(_selectedImages[index].path),
                              fit: BoxFit.cover,
                              width: 80,
                              height: 80,
                            ),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedImages.removeAt(index);
                          });
                        },
                        child: const NeuCard(
                          margin: EdgeInsets.zero,
                          padding: EdgeInsets.all(2),
                          backgroundColor: Color(0xFFFF6B6B),
                          child: Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// Builds the action toolbar content without Positioned wrapper
  // Widget _buildActionToolbarContent() {
  //   return Container(
  //     padding: const EdgeInsets.all(8.0),
  //     color: Colors.black.withValues(alpha: 0.5),
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.spaceAround,
  //       children: [
  //         ActionToolbarButton(
  //           icon: EvaIcons.text,
  //           label: 'Name',
  //           onTap: _showNameInputDialog,
  //         ),
  //         ActionToolbarButton(
  //           icon: EvaIcons.grid,
  //           label: 'Category',
  //           onTap: _showCategoryPickerDialog,
  //         ),
  //         ActionToolbarButton(
  //           icon: EvaIcons.messageSquare,
  //           label: 'Review',
  //           onTap: _showReviewInputDialog,
  //         ),
  //         ActionToolbarButton(
  //           icon: EvaIcons.star,
  //           label: 'Rating',
  //           onTap: _showRatingDialog,
  //         ),
  //         ActionToolbarButton(
  //           icon: EvaIcons.camera,
  //           label: 'Photo',
  //           onTap: _showImagePickerOptions,
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildTopButtons() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back Button
            GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: const NeuCard(
                padding: EdgeInsets.all(12),
                margin: EdgeInsets.zero,
                child: Icon(EvaIcons.arrowBack, color: Colors.black),
              ),
            ),
            // Submit Button
            GestureDetector(
              onTap: _isLoading ? null : _submitPlace,
              child: NeuCard(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                margin: EdgeInsets.zero,
                backgroundColor: const Color(0xFF00D2D3), // Cyan
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Row(
                        children: [
                          Icon(EvaIcons.checkmark, color: Colors.white),
                          SizedBox(width: 8),
                          Text(
                            'Submit',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
