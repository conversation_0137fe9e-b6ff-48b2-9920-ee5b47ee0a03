import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

/// A card to display a single product/service in the shop tab.
/// This version is robust and handles null values gracefully.
class ProductCard extends StatelessWidget {
  final Map<String, dynamic> productData;

  const ProductCard({super.key, required this.productData});

  @override
  Widget build(BuildContext context) {
    // --- THE FIX: Safely extract data with default fallbacks ---
    final String name =
        productData['product_name']?.toString() ?? 'Unnamed Product';
    final String description =
        productData['description']?.toString() ?? 'No description available.';
    final double price = (productData['price'] as num?)?.toDouble() ?? 0.0;
    final String type = productData['product_type']?.toString() ?? 'physical';
    final bool inStock = (productData['stock_count'] as int? ?? 0) > 0;

    // Safely get the first media image
    final List<dynamic> mediaList =
        productData['media'] as List<dynamic>? ?? [];
    final String? imagePath = mediaList.isNotEmpty
        ? mediaList.first?.toString()
        : null;
    // --- End of FIX ---

    Color typeColor;
    IconData typeIcon;

    switch (type) {
      case 'digital':
        typeColor = const Color(0xFFFFE66D); // Yellow
        typeIcon = Icons.cloud_download;
        break;
      case 'service':
        typeColor = const Color(0xFFFF6B6B); // Coral
        typeIcon = Icons.handyman;
        break;
      case 'physical':
      default:
        typeColor = const Color(0xFF4ECDC4); // Teal
        typeIcon = Icons.shopping_bag;
        break;
    }

    return NeuCard(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image
              NeuCard(
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(13),
                  child: SizedBox(
                    width: 80,
                    height: 80,
                    // Use a FutureBuilder to construct the full image URL
                    child: imagePath != null
                        ? FutureBuilder<String>(
                            future: ConfigService.instance.getBaseUrl(),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                final fullUrl =
                                    '${snapshot.data}/${imagePath.replaceAll('\\', '/')}';
                                return Image.network(
                                  fullUrl,
                                  fit: BoxFit.cover,
                                );
                              }
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            },
                          )
                        : Container(
                            color: Colors.grey.shade200,
                            child: const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                            ),
                          ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Product Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Price, Type, and Buy Button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'GHS ${price.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF6C5CE7), // Vibrant Blue
                    ),
                  ),
                  NeuChip(
                    label: type.toUpperCase(),
                    icon: typeIcon,
                    backgroundColor: typeColor,
                    textColor: typeColor == const Color(0xFFFFE66D)
                        ? Colors.black
                        : Colors.white,
                  ),
                ],
              ),
              NeuCard(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 10,
                ),
                margin: EdgeInsets.zero,
                shadowOffset: 4.0,
                borderWidth: 2.0,
                borderRadius: 20,
                backgroundColor: inStock
                    ? const Color(0xFF00D2D3)
                    : Colors.grey.shade300,
                child: Text(
                  inStock ? 'BUY NOW' : 'SOLD OUT',
                  style: TextStyle(
                    color: inStock ? Colors.white : Colors.grey[700],
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
